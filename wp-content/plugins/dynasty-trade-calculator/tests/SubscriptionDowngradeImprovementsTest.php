<?php

namespace DynastyTradeCalculator\Tests;

use PHPUnit\Framework\TestCase;
use DynastyTradeCalculator\Membership;

/**
 * Comprehensive test class for subscription downgrade improvements
 * Tests all the new functionality for handling downgrades properly
 */
class SubscriptionDowngradeImprovementsTest extends TestCase
{
    private $membership_handler;
    private $test_customer;
    private $test_membership;

    protected function setUp(): void
    {
        parent::setUp();
        $this->membership_handler = new Membership();
        
        // Mock WordPress functions and RCP objects for testing
        $this->setupMockEnvironment();
    }

    /**
     * Test that membership levels not in config are treated as free
     */
    public function testFreeMembershipLogicConsistency()
    {
        // Test with a membership level ID that's not in DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE
        $unmapped_membership = $this->createMockMembership(999); // ID 999 not in config
        
        $subscription_type = $this->membership_handler->getRotoGptSubscription($unmapped_membership);
        
        $this->assertEquals('free', $subscription_type, 'Unmapped membership levels should be treated as free');
    }

    /**
     * Test that proration messages are hidden for downgrades
     */
    public function testProrationMessageHidingForDowngrades()
    {
        // Mock a downgrade registration
        $this->setupMockDowngradeRegistration();
        
        $original_message = '<p>If you upgrade or downgrade your account, the new membership will be prorated up to $4.99 for the first payment.</p>';
        $filtered_message = $this->membership_handler->customizeProrationMessage($original_message);
        
        $this->assertEquals('', $filtered_message, 'Proration message should be hidden for downgrades');
    }

    /**
     * Test that proration messages are kept for upgrades
     */
    public function testProrationMessageKeptForUpgrades()
    {
        // Mock an upgrade registration
        $this->setupMockUpgradeRegistration();
        
        $original_message = '<p>If you upgrade or downgrade your account, the new membership will be prorated up to $4.99 for the first payment.</p>';
        $filtered_message = $this->membership_handler->customizeProrationMessage($original_message);
        
        $this->assertEquals($original_message, $filtered_message, 'Proration message should be kept for upgrades');
    }

    /**
     * Test custom downgrade message display
     */
    public function testCustomDowngradeMessageDisplay()
    {
        $this->setupMockDowngradeRegistration();
        
        // Capture output from the custom downgrade message
        ob_start();
        $this->membership_handler->addCustomDowngradeMessage();
        $output = ob_get_clean();
        
        $this->assertStringContainsString('Downgrade Information:', $output, 'Custom downgrade message should be displayed');
        $this->assertStringContainsString('will activate after your current subscription expires', $output, 'Message should explain activation timing');
    }

    /**
     * Test expiration date display customization for pending memberships
     */
    public function testExpirationDateDisplayForPendingMemberships()
    {
        $pending_membership = $this->createMockPendingMembership();
        $active_membership = $this->createMockActiveMembership();
        
        // Mock customer with both active and pending memberships
        $customer = $this->createMockCustomer([$active_membership]);
        $pending_membership->method('get_customer')->willReturn($customer);
        
        $original_date = 'December 31, 2025';
        $customized_date = $this->membership_handler->customizeExpirationDateDisplay($original_date, $pending_membership);
        
        $this->assertStringContainsString('Activates', $customized_date, 'Pending membership should show activation date');
        $this->assertStringContainsString('after current tier expires', $customized_date, 'Should explain when activation occurs');
    }

    /**
     * Test auto-renewal handling for downgrades
     */
    public function testAutoRenewalHandlingForDowngrades()
    {
        $old_membership = $this->createMockMembership(12); // Standard_200 tier
        $new_membership = $this->createMockMembership(9);  // Standard_100 tier (downgrade)
        
        $old_membership->method('is_recurring')->willReturn(true);
        $old_membership->expects($this->once())->method('set_recurring')->with(false);
        
        $new_membership->method('get_upgraded_from')->willReturn($old_membership->get_id());
        
        // Mock the membership retrieval
        $this->mockRcpGetMembership($new_membership->get_id(), $new_membership);
        $this->mockRcpGetMembership($old_membership->get_id(), $old_membership);
        
        $this->membership_handler->handleAutoRenewalForDowngrades($new_membership->get_id());
        
        // Verify that auto-renewal was disabled (assertion is in the mock expectation above)
    }

    /**
     * Test filtering of pending memberships from display
     */
    public function testFilterPendingMembershipsFromDisplay()
    {
        $active_membership = $this->createMockActiveMembership();
        $pending_membership = $this->createMockPendingMembership();
        
        $memberships = [$active_membership, $pending_membership];
        
        // Mock being on subscription page
        $this->mockSubscriptionPageContext();
        
        $filtered_memberships = $this->membership_handler->filterPendingMembershipsFromDisplay($memberships, []);
        
        $this->assertCount(1, $filtered_memberships, 'Pending memberships should be filtered out');
        $this->assertEquals($active_membership, $filtered_memberships[0], 'Active membership should remain');
    }

    /**
     * Test removal of upgrade buttons for pending memberships
     */
    public function testRemoveUpgradeButtonsForPendingMemberships()
    {
        $pending_membership = $this->createMockPendingMembership();
        
        $original_actions = '<a href="#" class="rcp_sub_details_change_membership"><button>Upgrade</button></a><br/><a href="#" class="rcp_sub_details_cancel"><button>Cancel</button></a>';
        $links = [
            '<a href="#" class="rcp_sub_details_change_membership"><button>Upgrade</button></a>',
            '<a href="#" class="rcp_sub_details_cancel"><button>Cancel</button></a>'
        ];
        
        $filtered_actions = $this->membership_handler->removeUpgradeButtonsForPendingMemberships($original_actions, $links, 1, $pending_membership);
        
        $this->assertStringNotContainsString('rcp_sub_details_change_membership', $filtered_actions, 'Upgrade buttons should be removed for pending memberships');
        $this->assertStringContainsString('rcp_sub_details_cancel', $filtered_actions, 'Other buttons should remain');
    }

    /**
     * Test downgrade detection logic
     */
    public function testDowngradeDetectionLogic()
    {
        // Test various tier combinations to ensure downgrade detection works correctly
        $test_cases = [
            ['from' => 12, 'to' => 9, 'expected' => true],   // standard_200 to standard_100 (downgrade)
            ['from' => 9, 'to' => 7, 'expected' => true],    // standard_100 to standard_50 (downgrade)
            ['from' => 7, 'to' => 8, 'expected' => true],    // standard_50 to free (downgrade)
            ['from' => 7, 'to' => 9, 'expected' => false],   // standard_50 to standard_100 (upgrade)
            ['from' => 8, 'to' => 7, 'expected' => false],   // free to standard_50 (upgrade)
        ];

        foreach ($test_cases as $case) {
            $from_membership = $this->createMockMembership($case['from']);
            $to_membership = $this->createMockMembership($case['to']);
            
            $from_subscription = $this->membership_handler->getRotoGptSubscription($from_membership);
            $to_subscription = $this->membership_handler->getRotoGptSubscription($to_membership);
            
            $is_downgrade = $this->isDowngrade($from_subscription, $to_subscription);
            
            $this->assertEquals($case['expected'], $is_downgrade, 
                "Downgrade detection failed for {$case['from']} to {$case['to']}");
        }
    }

    // Helper methods for testing

    private function setupMockEnvironment()
    {
        // Mock WordPress and RCP functions as needed for testing
        if (!function_exists('wp_get_current_user')) {
            function wp_get_current_user() {
                return (object) ['ID' => 1];
            }
        }
    }

    private function createMockMembership($level_id)
    {
        $membership = $this->createMock(\RCP_Membership::class);
        $membership->method('get_object_id')->willReturn($level_id);
        $membership->method('get_id')->willReturn(rand(1000, 9999));
        return $membership;
    }

    private function createMockActiveMembership()
    {
        $membership = $this->createMockMembership(9);
        $membership->method('get_status')->willReturn('active');
        return $membership;
    }

    private function createMockPendingMembership()
    {
        $membership = $this->createMockMembership(7);
        $membership->method('get_status')->willReturn('pending');
        return $membership;
    }

    private function createMockCustomer($memberships = [])
    {
        $customer = $this->createMock(\RCP_Customer::class);
        $customer->method('get_memberships')->willReturn($memberships);
        return $customer;
    }

    private function setupMockDowngradeRegistration()
    {
        // Mock RCP registration for downgrade scenario
        global $rcp_registration;
        $rcp_registration = $this->createMock(\RCP_Registration::class);
        $rcp_registration->method('get_registration_type')->willReturn('downgrade');
        
        $membership = $this->createMockActiveMembership();
        $membership->method('get_expiration_date')->willReturn('2025-12-31 23:59:59');
        $rcp_registration->method('get_membership')->willReturn($membership);
    }

    private function setupMockUpgradeRegistration()
    {
        global $rcp_registration;
        $rcp_registration = $this->createMock(\RCP_Registration::class);
        $rcp_registration->method('get_registration_type')->willReturn('upgrade');
    }

    private function mockSubscriptionPageContext()
    {
        global $rcp_options;
        $rcp_options = ['subscription_page' => 123];
        
        // Mock WordPress is_page function
        if (!function_exists('is_page')) {
            function is_page($page_id) {
                return $page_id === 123;
            }
        }
    }

    private function mockRcpGetMembership($id, $membership)
    {
        // In a real test environment, you'd mock the rcp_get_membership function
        // For now, this is a placeholder for the mocking logic
    }

    private function isDowngrade($from_subscription, $to_subscription)
    {
        $rotogpt_subscriptions = array_column(DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE, 'rotogpt_subscription_type');
        $from_index = array_search($from_subscription, $rotogpt_subscriptions);
        $to_index = array_search($to_subscription, $rotogpt_subscriptions);
        
        return ($to_index < $from_index);
    }
}
