<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitd4dec3e032fc3995173d8b893ea3203e
{
    public static $prefixLengthsPsr4 = array (
        'D' => 
        array (
            'DynastyTradeCalculator\\' => 23,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'DynastyTradeCalculator\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitd4dec3e032fc3995173d8b893ea3203e::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitd4dec3e032fc3995173d8b893ea3203e::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInitd4dec3e032fc3995173d8b893ea3203e::$classMap;

        }, null, ClassLoader::class);
    }
}
