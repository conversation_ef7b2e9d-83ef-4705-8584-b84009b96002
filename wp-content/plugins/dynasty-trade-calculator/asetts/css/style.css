@import url('https://fonts.googleapis.com/css2?family=Familjen+Grotesk:ital,wght@0,400..700;1,400..700&display=swap');

/* Hide pending memberships from subscription page to reduce user confusion */
.rcp-table .dtc-pending-membership,
#rcp-account-overview .dtc-pending-membership {
    display: none !important;
}

/* Hide pending membership explanatory text styling */
.dtc-pending-explanation {
    font-style: italic;
    color: #666;
    margin-top: 10px;
    padding: 10px;
    background-color: #f9f9f9;
    border-left: 4px solid #ddd;
}

.dtc-col{float:left;padding:2.5px}
.dtc-col-5{width:calc(5% - 2.5px);}
.dtc-col-10{width:calc(10% - 2.5px);}
.dtc-col-20{width:calc(20% - 2.5px);}
.dtc-col-25{width:calc(25% - 2.5px);}
.dtc-col-30{width:calc(30% - 2.5px);}
.dtc-col-35{width:calc(35% - 2.5px);}
.dtc-col-40{width:calc(40% - 2.5px);}
.dtc-col-50{width:calc(50% - 2.5px);}
.dtc-col-60{width:calc(60% - 2.5px);}
.dtc-col-70{width:calc(70% - 2.5px);}
.dtc-col-80{width:calc(80% - 2.5px);}
.dtc-col-90{width:calc(90% - 2.5px);}

.isa_info, .isa_success, .isa_warning, .isa_error {
margin: 10px 0px;
padding:12px;
 
}
.isa_info {
    color: #00529B;
    background-color: #BDE5F8;
}
.isa_success {
    color: #4F8A10;
    background-color: #DFF2BF;
}
.isa_warning {
    color: #9F6000;
    background-color: #FEEFB3;
}
.isa_error {
    color: #D8000C;
    background-color: #FFD2D2;
}
.isa_info i, .isa_success i, .isa_warning i, .isa_error i {
    margin:10px 22px;
    font-size:2em;
    vertical-align:middle;
}

.dtc-about-content {margin:0px auto !important;background-color:#000 !important;color:#FFF !important;padding:50px !important;font-size:1.1em}
.dtc-about-content .fusion-row{max-width:980px !important;background-color:#333}
.dtc-about-content p,.dtc-about-content h2,.dtc-about-content h3{color:#FFF !important}
.dtc-about-content p strong{color:#E1B830}

.dtc-about-left{border-right:1px solid #FFF}


.dtc-mfl-overlay-front {
    /* position: absolute; */
    width: 100%;
    text-align: center;
    padding: 50px;
    color:#000;
}

.dtc-settings-card {
    background-color: #e0b82f;
    width: 320px;
    padding: 5px;
    margin: 0px 10px;
	margin-top: -10px;
   
}
.dtc-settings-card span {
    margin-right: 10px;
    color: #000;
	font-weight:bold;
	    font-size: 11px;
    
}


.dtc-stats-trade-suggestion-content h3{color:#FFF}
.dtc-trade-item{background-color:#EFEFEF;padding:10px;margin:10px;text-align:left;}
.dtc-trade-item .dtc-trade-team-one,.dtc-trade-item .dtc-trade-team-two{float:left;width:50%; text-align:center;}
.dtc-trade-item h4{text-align:center;font-weight:16px;    font-size: 18px; 
    margin: 5px 0px;}
.dtc-trade-item h4 a{color:#000;}
.dtc-trade-item .dtc-trade-team-one h5{background-color: #CCC;
    padding: 5px;margin:5px 0px;}
.dtc-trade-item .dtc-trade-team-two h5{background-color: #CCC;
    padding: 5px;margin:5px 0px;}
.ranking-chart-item{height:350px;width:100%;}
	.dtc-team-trade-item{border-bottom:1px dotted #CCC;padding:4px;}
	
	.dtc-player-card-left{
	float: left;
    width: 70%;
    text-align: left;
}

	.dtc-player-card-left img{
   
    float: left;}
	.dtc-player-card-right{
	float:left;
	padding-top: 10px;
	width:30%;	
	}
	.ranking-chart-item{background-color:#000;margin:10px 0px;}
	.dtc-stats-player-card{margin:10px;border-bottom:2px solid #e0b82f;font-family: "Trajan Pro", Arial, Helvetica, sans-serif;}
	.dtc-stats-player-card,.dtc-stats-player-card .dtc-player-card-left h2 { color: #FFF;}
	 
	  .dtc-player-card-right p {     color: #FFF;
    text-align: right;
    padding: 0px;
    margin: 0px;
    font-size: 18px;
	margin-right:5px;}
	.dtc-stats-player-card h2{margin-top: 0px;margin-bottom:0px;
    font-size: 25px;}
	.dtc-player-card-right h2 { line-height: 40px;
    color: #e0b82f;
    text-align: right;
    font-size: 48px;
    font-weight: 500;
    padding: 0px;
    margin: 0px;}
	
	.dtc-player-card-left .dtc-calc-item-player-info-stats{font-size:16px;}
	.dtc-player-card-left .dtc-calc-item-player-info-stats span{margin-right:15px;}
	.remodal-grey {background-color:#343536 !important}
	.remodal-black {background-color:#262626 !important}
	.stat-years,.stat-years option{margin-right:15px;background-color:#000;color:#FFF;border:none;padding:4px;}

@media (max-width: 768px) {
.dtc-player-card-left img {
float:none;
}
}
.dtc-leauge-option {
    bottom: 0px!important;
    font-size: 17px !important;
    height: auto!important;
    left: 0px!important;
    line-height: 25px !important;
    min-height: 50%;
    min-width: 50%;
    padding-bottom: 0px;
    padding-left: 0px;
    padding-right: 0px;
    padding-top: 3px;
    position: relative!important;
    right: 0px!important;
    top: 0px!important;

}
.dtc-filter-toggles {color:#FFF;text-align:center;}
.dtc-badge-logo{float:right;}

.dtc-calculator-wrapper{margin:0px auto;margin-bottom:20px;min-width:320px; background-color:#000 }
.dtc-calculator-wrapper a { text-decoration: none; }

.dtc-calc-header{background-color:#e0b82f; background-image:url(../images/arrows.png); background-repeat:no-repeat; background-position:center ;border-top-left-radius: 10px;border-top-right-radius: 10px;background-position: top;
    background-size: contain;}
.dtc-calc-header-left{ text-align:center;float:left;width:50%;font-size:40px;}
.dtc-calc-header-right{ text-align:center;float:left;width:50%;}
.dtc-calc-header h3  {font-size:25px !important;font-weight:bold;line-height:45px !important;color:#333 !important;padding-top:5px;padding-left:20px;padding-right:20px; text-transform:uppercase;margin:0px !important;}

.dtc-player-picker{float:left;width:50%;}

.dtc-teams-size-buttons { font-size: 12px; line-height: 25px; }
.dtc-teams-size-buttons a{width:24.75%;} 
/* Commented out empty ruleset */
/*.dtc-teams-filter-buttons{;}*/

.dtc-filters-wrapper{padding:10px;margin-bottom:20px;}
.dtc-filters h3{color:#FFF;font-size:14px;line-height:14px;margin-top:10px !important;margin-bottom: 10px !important; text-align:center}
.left-top-round{border-top-left-radius: 10px;}
.left-bottom-round{border-bottom-left-radius: 10px;}
.right-bottom-round{border-bottom-right-radius: 10px;}
.right-top-round{border-top-right-radius: 10px;}

.right-round{border-top-right-radius: 10px;border-bottom-right-radius: 10px;}
.left-round{border-top-left-radius: 10px;border-bottom-left-radius: 10px;}
.dtc-filter-buttons a{float:left;background-color:#343535;display:block;padding:3px 7px;color:#FFF !important;font-size:12px;line-height:25px;border-right:1px solid #282828; text-align:center;font-family: "Trajan Pro", Arial, Helvetica, sans-serif;}
.dtc-filter-buttons .filter-active{background-color:#e0b82f;color:#333 !important; text-decoration:none}
.dtc-yellow-button{background-color: #e0b82f;
    color: #333 !important;
    text-decoration: none;
    padding: 10px 40px;
    margin: 0px 10px;
    font-weight: bold;}

.dtc-idp-actions .mobile-text,.dtc-offense-actions .mobile-text{display:none}
	.dtc-top-extra .mobile-text,.dtc-top-extra .mobile-text{display:none}
	
	
.dtc-filter-buttons .filter-active{}
.dtc-fifty-button a{width:38%;}
.dtc-thirty-button a{width:33%;}
.dtc-half-button a{width:49.5%;}
.dtc-border-top a{border-top:1px solid #282828;}
.dtc-player-input{ background-color:#343535;border-bottom:#CCC;height:110px;padding-top:10px;}
.dtc-player-input{border-bottom:1px solid #b1b2b2;}

.dtc-player-picker-left .dtc-player-input,.dtc-player-picker-left  .dtc-player-input-total{border-right:1px solid #1a1a1a;}
.dtc-player-picker-right .dtc-player-input,.dtc-player-picker-right  .dtc-player-input-total{border-left:1px solid #1a1a1a;}

.dtc-player-input-total{background-color:#343535;height:70px;}

.dtc-add-player{display:block; background-image:url(../images/addplayerpick.png); background-repeat:no-repeat; background-position:center; background-size:contain;color:#fff !important;background-size: contain;line-height:12px;text-align:center; height:100%;    font-family: trajan pro!important;
    font-size: 15px!important;}
.dtc-val{margin-right:5px;}

.dtc-calc-item .dtc-calc-item-image img{border-style: none;
    vertical-align: top;
    max-width: 100%;
    height: auto;}
.dtc-calc-item .dtc-calc-item-image{float:left;min-height:60px;padding:0px 5px 0px 8px;}
.dtc-calc-item .dtc-calc-item-player-info{float:left;width:50%;min-height:60px;}
.dtc-calc-item .dtc-calc-item-player-score{float:right;width:25%;font-size:35px;line-height:35px;color:#e0b82f;min-height:60px; text-align:right; padding-right:5px;}
.dtc-calc-item .dtc-calc-item-player-score span,.dtc-calc-item .dtc-calc-item-player-score-mobile span{font-size:12px;line-height:12px;color:#FFF;display:block; text-align:right}
.dtc-calc-item .dtc-calc-item-player-info h4,.dtc-player-name-mobile{font-size:13px;color:#fff !important;padding:1px;margin:0px;}
.dtc-calc-item .dtc-calc-item-player-info-stats,.dtc-calc-item .dtc-calc-item-player-info-stats-mobile{font-size:11px;line-height:11px;color:#fff;margin-top:7px;}
.dtc-calc-item .dtc-calc-item-player-info-stats span {margin-left:10px;}
.dtc-calc-item .dtc-calc-item-player-info-actions,.dtc-calc-item .dtc-calc-item-player-info-actions-mobile{font-size:14px;line-height:10px;color:#fff;margin-top: 10px;}
.dtc-calc-item .dtc-calc-item-player-info-actions a,.dtc-calc-item .dtc-calc-item-player-info-actions-mobile a{font-size:14px;line-height:10px;color:#fff;}
.dtc-calc-item .dtc-calc-item-player-info-actions span {margin-left:15px;font-size:10px;line-height:10px;}



.dtc-calc-item-inner{padding:5px;}
dtc-calc-item{}
.dtc-player-input-total h3{font-size:35px !important;line-height:30px;color:#FFF;float:left;width:60%; text-align:center;font-weight:400;margin-top:10px;}
.dtc-right-total,.dtc-left-total{font-size:40px !important;color:#e0b82f!important;padding-top:10px; text-align:right;padding-right:10px;line-height:40px;font-weight:bold;}
.dtc-player-input-total span{display:block;text-align:right;color:#FFF;padding-right:10px;font-size:12px;line-height:12px;margin-top:5px;}
.dtc-calc-item-player-info-stats-mobile{display:none;}
.dtc-calc-item-player-score-mobile{display:none;}
.dtc-calc-item-player-info-actions-mobile{display:none;}

.dtc-calc-item .dtc-calc-item-player-info-stats-pics,.dtc-calc-item .dtc-calc-item-player-info-stats-pics{font-size:12px !important}


.dtc-filter-buttons {margin-top:0px;}


.dtc-under-calc-buttons {float:left;width:48%;margin:0 1% 10px 1%;padding:10px 10px;}
.dtc-loader{text-align:center;padding:20px;}

 .dtc-loading-overlay{  opacity:0.8;

    background-color:#333;

    position:fixed;

    width:100%;

    height:100%;

    top:0px;

    left:0px;

    z-index:1000;}

                

                .dtc-loading-overlay img{ position:absolute;top:50%;left:50%;z-index:99999999 }

.dtc-player-card-wrapper{margin-top:30px;}
.dtc-player-card-tabs a {
    float: left;
    padding: 5px 10px;
    margin-right: 1px; 
  background-color:#343536;color:#FFF !important
	

}
.dtc-player-card-tabs a.active{  background-color: #e0b82f;
    color: #000 !important;}
.dtc-player-profile-tab{padding:10px;display:none;clear:both;background-color:#343536}


.dtc-calc-item-player-info-stats{color:#FFF; text-align:left;}
.dtc-calc-item-player-info-stats h2{color:#FFF}
.dtc-player-card-right .dtc-badge-logo {
    float: right !important;
}
.dtc-filters-wrapper h3 {
    margin-bottom: 5px !important;
    margin-top: 20px !important;
}
.dtc-image-item-wrapper{height:80px;width:80px;}
.dtc-player-card-left .dtc-image-item-wrapper{height:180px;width:180px;}
.dtc-calc-item-image .dtc-image-item-wrapper{height:80px;width:80px;}
.dtc-calc-item-image-pick{height:80px !important;width:80px  !important;}
.update-player-photo-form-image .dtc-image-item-wrapper{height:180px;width:180px;}
.clipcast-header{width:85px}
.clipcast-button-small a{ background-image:url(../images/clipcast-wide-black.png);background-repeat:no-repeat !important;background-position:center;display:block;width:70px;background-size:100%;}

.clipcast-button-wide{position: absolute;
    right: 30px;
   
    margin-top:-50px}
.clipcast-button-wide img{width:100px}
.MuiDrawer-modal{z-index:99999999999999999999999999999999999999999 !important}
@media (max-width: 1606px) {
    .dtc-idp-actions .mobile-text,.dtc-offense-actions .mobile-text{display:block;}
	.dtc-idp-actions .desktop-text,.dtc-offense-actions .desktop-text{display:none}
}
@media (max-width: 1024px) {

.dtc-right-total,.dtc-left-total{font-size:25px !important ;line-height:25px;}	
.dtc-calc-item .dtc-calc-item-player-score-mobile{font-size:20px;line-height:20px;}

.dtc-calc-item .dtc-calc-item-player-score {
   font-size: 30px;
    line-height: 30px;
}
.dtc-player-input-total h3{font-size:25px !important;line-height:25px;}

}


@media (max-width: 825px) {
	.dtc-about-left{border-right:none}
}
.dtc-player-name-mobile{display:none;}
@media (max-width: 825px) {
	
	
	.clipcast-header{width:45px}
.clipcast-button-small a{ background-image:url(../images/clipcast-small.png);background-position:center;display:block;width:30px;height:30px;background-size:100%;}

	
	
	
	.clipcast-button-wide{
    margin-top:-40px}
	
    .dtc-calc-item .dtc-calc-item-image .dtc-image-holder-bg{margin-top:2px;}
    
	.dtc-top-extra .mobile-text,.dtc-top-extra .mobile-text{display:block}
	.dtc-top-extra .desktop-text,.dtc-top-extra .desktop-text{display:none}
	.dtc-badge-logo {
        float: left;
        display: none;
    }
	
	.dtc-under-calc-buttons {display:block;}
	.dtc-calc-item .dtc-calc-item-player-info-actions,.dtc-calc-item .dtc-calc-item-player-info-actions-mobile{margin-top:5px;}
	.dtc-calc-item .dtc-calc-item-image-pick img{width:60px;}
    .dtc-calc-item-image .dtc-image-item-wrapper{height:60px;width:60px;margin:3px;}
	.dtc-calc-item-image-pick{height:60px !important;width:60px  !important;}
	.dtc-calc-item .dtc-calc-item-player-info-actions a,.dtc-calc-item .dtc-calc-item-player-info-actions-mobile a{font-size:14px;margin-right:5px;}
	.dtc-add-player{   background-size: 100% auto;margin:0px auto;    background-position: center 50px; padding-top: 20px;}
	.dtc-player-input{padding-top:20px;}
	.dtc-calc-item-player-info-actions-mobile{display:block;}
	.dtc-calc-item-player-info-actions-mobile.normal-mode.pick-template{display:none;}
.dtc-register-now a{color:#FFF !important}
	.dtc-register-now a:hover{color:#FFF !important}
	.dtc-player-input-total h3{font-size:18px !important;line-height:18px;padding-top:5px;width:40%;}
	.dtc-calc-item-player-info-actions-mobile{ text-align:center;margin-top:5px; text-align:center}
	.dtc-right-total,.dtc-left-total{font-size:30px !important ;line-height:30px;}
	.dtc-calc-item-player-score{display:none;}
	.dtc-calc-item-player-score-mobile{display:block;}
	.dtc-calc-item .dtc-calc-item-player-score-mobile{font-size:25px;line-height:25px;color:#e0b82f; text-align:right}
	.dtc-player-input {padding-top:5px;height:135px;}
	.dtc-calc-item-player-info-actions{display:none;}
	.dtc-calc-item-player-info-actions.startup-mode{display:block;}
	.dtc-calc-item-player-info-actions.normal-mode.pick-template{display:block;}
	.dtc-calc-item .dtc-calc-item-player-info{text-align:right;}
	.dtc-calc-item-player-info-stats-mobile{display:block;text-align:center;}
	.dtc-calc-item .dtc-calc-item-player-info-stats{display:none;}
	.dtc-calc-item .dtc-calc-item-image{float:left;}
	.dtc-calc-item .dtc-calc-item-player-info{float:right;}
	.dtc-calc-item .dtc-calc-item-player-score{float:none;width:auto;clear:both}
	
    .fusion-header-has-flyout-menu .fusion-flyout-menu-icons .fusion-flyout-menu-toggle {
        padding: 9px 20px !important;
    }

}

@media (max-width: 500px) {
		
		.dtc-settings-card {
    width:auto
}
		
	.dtc-player-name-mobile{display:block;padding:0px;margin:0px; text-align:center;}
	.dtc-player-name{display:none;}

	.dtc-player-name-mobile{display:block;padding:0px;margin:0px; text-align:center;}
	.dtc-player-name{display:none;}
}

@media (min-width: 1200px) {
    .dtc-settings-outer {
        display: flex;
        gap: 20px;
    }
    .dtc-league-format-buttons h3,
    .dtc-settings-buttons h3 {
        margin-top: 0 !important;
    }
}

@media (min-width: 825px) {
    .dtc-settings-middle {
        display: flex;
        gap: 20px;
        flex-grow: 1;
    }
    .dtc-scoring-options-buttons h3 {
        margin-top: 0 !important;
    }
}

.dtc-settings-inner {
    flex-grow: 1;
}

.dtc-settings-toggle h3 {
    font-size: 20px !important; 
    text-align:left !important;
    cursor: pointer;
}

.dtc-settings-toggle.dtc-open .fa-caret-up { display: inline-block; }
.dtc-settings-toggle.dtc-open .fa-caret-down { display: none; }
.dtc-settings-toggle.dtc-closed .fa-caret-up { display: none; }
.dtc-settings-toggle.dtc-closed .fa-caret-down { display: inline-block; }

.dtc-filters-wrapper {
    display: none;
}

#side-header-sticky {
    height: 83px;
}

#side-header {
    position: fixed !important;
    top: 0;
}

.page-id-11916 #side-header {
    padding-bottom: 7px !important;
    border-bottom: 1px solid;
    border-color: #e1b830;
}

.page-id-11916 .avada-page-titlebar-wrapper {
    display: none;
}

img.fusion-mobile-logo {
    height: 56px !important;
}

a.fusion-icon.fusion-icon-search {
    text-decoration: none;
}

.dtc-calculator-wrapper-remove {
    max-width: 1200px;
    margin: auto;
}

.with-rotogpt .dtc-player-input-total.left-bottom-round {
    border-bottom-left-radius: 0px;
}
.with-rotogpt .dtc-player-input-total.right-bottom-round {
    border-bottom-right-radius: 0px;
}

.dynasty_settings {
    float: right;
}

/* LEAGUE INTEGRATION AREA */
.calc_settings_header_area {
    margin-bottom: 10px;
    /* margin-top: 5px; */
}

#league_selector {
    display: flex;
    align-items: center;
}

#league_connect>a {
    text-decoration: none;
    color: inherit;
}

#connect_league_button {
    font-family: "Familjen Grotesk", sans-serif;
    color: #000000 !important;
    padding: 0;
    border: none;
    background: #E2B833;
    border-radius: 8px;
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 2px;
    padding-bottom: 2px;
    text-decoration: none;
    margin-top: 5px;
}

#platform_logo {
    /* height: 60px; */
    background: #646464;
    width: 60px;
    padding: 8px;
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
    float: left;
}

#platform_logo>img {
    max-width: 100%;
    max-height: 100%;
}

#platform_name {
    float: left;
    margin-left: 10px;
    font-family: "Familjen Grotesk", sans-serif;
    color: #FFFFFF;
    font-size: 1.2rem;
}

#league_name {
    float: left;
    margin-left: 6px;
    font-family: "Familjen Grotesk", sans-serif;
    color: #FFFFFF;
    font-size: 1.2rem;
}

#league_button_area {
    float: left; 
    padding-left: 15px;
    align-self: flex-start;
    min-width: 62px;
}

#edit_league_button {
    float: left; 
    padding-top: 5px;
}

#close_league_button {
    float: left;
    padding-left: 5px;
    padding-top: 5px;
}

.dynasty_settings {
    padding-left: 20px;
    margin-top: 2px;
}

.league-import-item, 
.league-import-item select,
.league-import-button a {
    font-family: "Open Sans";
}

.league-import-item h1 {
    font-family: "Trajan Pro";
}

.league-import-item, 
.league-import-item select,
.league-import-button a {
    font-family: "Open Sans";
}

.league-import-item h1 {
    font-family: "Trajan Pro";
}

@media (max-width: 1000px) {
    .dtc-settings-toggle h3 {
        font-size: 15px !important; 
    }

    #platform_name {
        font-size: 0.9rem;
    }
    
    #league_name {
        font-size: 0.9rem;
    }

    #league_button_area {
        padding-left: 5px;
    }

    #edit_league_button img {
        width: 15px;
    }

    #close_league_button img {
        width: 15px;
    }
}

@media (max-width: 500px) {
    #platform_name {
        display: none;
    }

    .dtc-settings-toggle h3 {
        font-size: 12px !important;
    }
}

.dtc-pricing-login-button {
    z-index: 999;
}

.fusion-mobile-nav-item>a {
    text-decoration: none;
}

/* Subscription Page : Starts */
#rcp_registration_form {
    max-width: 540px;
    margin: 0 auto;
}

#rcp_registration_form label {
    color: #000;
}

#rcp_subscription_levels label {
	margin-left: 15px;
	color: #000;
}

#rcp_subscription_levels label:hover,
#rcp_discount_code_wrap label:hover,
#rcp_payment_gateways label:hover {
	color: #e1b830;
}

#rcp_subscription_levels .rcp_subscription_level_name {
	font-weight: bold;
}

.rcp_subscription_level {
    display: flex;
}

.rcp_gateway_fields {
    margin-top: 20px;
}

#rcp_submit {
    font-weight: bold;
    font-size: large;
    padding: 5px 10px;
}
/* Subscription Page : Ends */

/* Calculator Page : Starts */
.league-import-item .mfl-trade-table td {
    border: none !important;
}
/* Calculator Page : Ends */

/* Trade Wire Page : Starts */
.dtc-calc-item-image-pick > div {
    min-width: 60px;
}

.dtc-pick-template-rookie .dtc-calc-item-inner {
    margin-left: 15px;
}
/* Trade Wire Page : Ends */

/* Media Queries : Starts */
@media (max-width: 767px) {
    .mfl-trade-table-team {
        display: block;
    }

    .mfl-trade-table-player {
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

@media (max-width: 400px) {
    .mfl-trade-table-player {
        max-width: 60px;
    }
}

@media (max-width: 450px) {
    .dtc-player-input {
        height: 140px;
    }

    .dtc-calc-item .dtc-calc-item-player-info-stats-mobile {
        max-width: 60px;
    }
}

@media (max-width: 500px) {
    .mfl-trade-table-player {
        max-width: 80px;
    }
}
/* Media Queries : Ends */

#dtc-integration-overlay{padding:10px;background-color:#FFF;margin:50px auto;width:100px;text-align:center;; border-radius: 50%;}
#dtc-integration-overlay h3{color:#000;}

#yahoo-login-overlay{padding:10px;background-color:#FFF;margin:50px auto;width:100px;text-align:center;; border-radius: 50%;}
#yahoo-login-overlay h3{color:#000;}

.yahoo-login {
    background-color:#FFF;
    color:#000;
}

.yahoo-login {
    #yahoo-login-text {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        color:#000000;
        margin-top: -10px;
        font-family: 'Open Sans';
        font-weight: 600;
    }

    #yahoo-login-button,  #submit_auth_code {
        border-radius: 26px;
        height: 40px;
        font-size: 14px;
        color: #FFF;
        background-color: #5F01D1;
        text-align: center;
        font-weight: 600;
        min-width: 100px;
        display: inline-block;
        cursor: pointer;
        font-family: 'Open Sans';
    }

    #yahoo-login-button {
        width: 160px;
    }

    .yahoo-label {
        display: inline-block;
        margin-right: 10px;
        color:#000000;
        font-family: 'Open Sans';
        font-weight: 600;
    }

    #auth_code {
        display: inline-block;
        width: 200px;
        padding: 8px;
        margin-right: 10px;
        box-sizing: border-box;
        border-radius: 10px;
        border: 1px solid #5F01D1;
    }
}

.ffpc-loader select { width:100%; }

.ffpc-login {
    background: #00359F;

    #ffpc-login-text {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        color:#4293F6;
        margin-top: 0px;
        font-family: 'Open Sans';
        font-weight: 600;
    }

    #ffpc_submit_button {
        border-radius: 26px;
        height: 40px;
        width: 160px;
        font-size: 14px;
        color: #FFF;
        background-color: #4293F6;
        text-align: center;
        font-weight: 600;
        min-width: 100px;
        display: inline-block;
        cursor: pointer;
        font-family: 'Open Sans';
        border: 2px solid #FFFFFF;
        margin: 10px;
    }

    .ffpc-label {
        display: inline-block;
        margin-right: 10px;
        color:#4293F6;
        font-family: 'Open Sans';
        font-weight: 600;
    }

    input[type=text] {
        border: 2px solid #4293F6;
    }

    select {
        border: 2px solid #4293F6;
        width: 80%;
        border-radius: 5px;
    }
}

#ffpc-login-overlay{padding:10px;background-color:#FFF;margin:50px auto;width:100px;text-align:center; border-radius: 50%;}
#ffpc-login-overlay h3{color:#000;}



.fantrax-loader select { width:100%; }

.fantrax-login {
    background: #000000;

    #fantrax-login-text {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        color:#4293F6;
        margin-top: 0px;
        font-family: 'Open Sans';
        font-weight: 600;
    }

    #fantrax_submit_button {
        border-radius: 26px;
        height: 40px;
        width: 160px;
        font-size: 14px;
        color: #FFF;
        background-color: #4293F6;
        text-align: center;
        font-weight: 600;
        min-width: 100px;
        display: inline-block;
        cursor: pointer;
        font-family: 'Open Sans';
        border: 2px solid #FFFFFF;
        margin: 10px;
    }

    .fantrax-label {
        display: inline-block;
        margin-right: 10px;
        color:#4293F6;
        font-family: 'Open Sans';
        font-weight: 600;
    }

    input[type=text] {
        border: 2px solid #4293F6;
    }

    select {
        border: 2px solid #4293F6;
        width: 80%;
        border-radius: 5px;
    }
}

#fantrax-login-overlay{padding:10px;background-color:#FFF;margin:50px auto;width:100px;text-align:center; border-radius: 50%;}
#fantrax-login-overlay h3{color:#000;}

.ffpc_your_team, .ffpc_load_opposing_team, .fantrax_your_team, .fantrax_load_opposing_team, .sleeper_api_your_team, .sleeper_api_load_opposing_team {
    width: 100%;
}

#rcp_auto_renew_wrap {
    display: none !important;
}
