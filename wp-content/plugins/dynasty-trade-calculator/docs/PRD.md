# Dynasty Trade Calculator - PRD

## Status (June 20, 2025)

### ✅ Completed
- **Fatal Error Fix**: Resolved PHP dependency issues, calculator loads without errors
- **Class-Based Migration**: Migrated membership functionality from functions to classes
- **Test Suite**: 50 tests, 370 assertions - ALL PASSING ✅
- **Debug System**: Conditional logging with `DTC_DEBUG` and `DTC_DEBUG_VERBOSE` constants
- **Shortcodes**: Recovered missing `dtc_register_form` and `dtc-easy-pricing-table`
- **Proration Fix**: Disabled RCP proration for downgrades to prevent extended expiration dates

### 🔧 Debug Configuration (Essential)
Add to `wp-config.php`:
```php
// Basic debug (production: false)
define('DTC_DEBUG', true);
// Verbose debug with object dumps (production: false)
define('DTC_DEBUG_VERBOSE', true);
```

## 📋 Task List

### 🔄 In Progress
- [ ] **Update Remaining Files**: Find and replace old membership functions with class methods
- [ ] **Calculator Class Migration**: Convert calculator functionality to class-based approach
- [ ] **API Class Migration**: Convert REST API functionality to class-based approach
- [ ] **Admin Class Migration**: Convert admin functionality to class-based approach

### ⏳ Pending
- [ ] **Remove Old Files**: Delete function-based files after migration complete
- [ ] **Expand README.md**: Add proper project documentation for GitHub
- [ ] **Performance Testing**: Verify no performance regression after migration
- [ ] **Production Deployment**: Deploy class-based version to production

### 🎯 Current Priority
**Complete function-to-class migration** - Search for old function calls and replace with class methods

## 🏗️ Architecture
```
src/
├── Plugin.php      # Main initialization
├── Membership.php  # Complete membership functionality
├── Debug.php       # Conditional logging
└── RestApi.php     # REST API endpoints
```

## 🔧 Proration Fix (June 25, 2025)

### Problem
RCP's default proration system was extending expiration dates for downgrades. When downgrading from $9.99 to $4.99, RCP calculated remaining value and extended the new membership to 2 months instead of the standard 1 month.

### Solution
Implemented selective proration disabling + proper expiration date calculation:
- **Downgrades**: Proration disabled, standard membership duration used
- **Upgrades**: Proration enabled, extended duration for remaining value
- **Same Tier**: Proration enabled, normal RCP behavior
- **Pending Memberships**: Correct expiration date calculation (start date + duration)

### Implementation
- `disableProrationCreditForDowngrades()`: **NEW** - Disables proration calculation entirely for downgrades (early hook)
- `disableProrationForDowngrades()`: Returns 0 for downgrades, original amount for upgrades (backup filter)
- `adjustExpirationLengthForDowngrades()`: Returns standard duration for downgrades
- **Fixed**: Pending membership expiration date calculation in downgrade scheduling
- **Fixed**: Proration display bug - downgrades now show $0 proration instead of higher tier price
- Uses subscription type hierarchy from `DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE`

### Example
- Current: $9.99 plan expires July 25, 2025
- Downgrade: $4.99 plan starts July 25, expires August 25, 2025 (1 month duration)
- No proration extension applied
- **Display Fix**: Registration form shows $0 proration credit instead of $4.99

## Proration Display Bug Fix (June 2025)

### Problem
RCP registration form was showing incorrect proration amounts during downgrades. When downgrading from $4.99 to $2.99, the form displayed "-$4.99" proration credit instead of "$0" as expected.

### Root Cause
The `rcp_membership_get_prorate_credit` filter was applied too late in the process. The registration form called `get_prorate_credit_amount()` which calculated the full proration amount before our filter could disable it.

## Subscription Downgrade Improvements (June 2025)

### Overview
Comprehensive improvements to the Dynasty Trade Calculator subscription system's downgrade functionality to enhance user experience and reduce confusion around subscription management.

### Key Improvements

#### 1. Free Membership Logic Consistency
- **Implementation**: All subscription handling methods consistently treat membership levels not present in `DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE` as free subscriptions
- **Logic**: Uses `DTC_ROTOGPT_TEST_DRIVE_SUBSCRIPTION` fallback (defined as 'free') in `getRotoGptSubscription` method
- **Benefit**: Ensures consistent behavior across all subscription operations

#### 2. Enhanced Proration Message Handling
- **Problem**: Users saw confusing proration messages like "we are doing prorate amount $4.99 max" during downgrades
- **Solution**:
  - Hide proration messages entirely for downgrades using `customizeProrationMessage` filter
  - Display clear custom message explaining tier activation timing via `addCustomDowngradeMessage`
  - Keep original proration messages for upgrades
- **User Experience**: Clear communication about when downgraded tier will activate

#### 3. Corrected Expiration Date Display
- **Problem**: Expiration dates showed misleading information for pending downgrades
- **Solution**: Custom `customizeExpirationDateDisplay` filter shows "Activates [date] (after current tier expires)" for pending memberships
- **Benefit**: Users understand exactly when their new tier will become active

#### 4. Auto-Renewal Management
- **Implementation**: `handleAutoRenewalForDowngrades` method automatically disables auto-renewal on old membership when downgrade activates
- **Logic**: Prevents conflicts where old tier might renew when pending downgrade should activate
- **Behavior**: Current plan expires naturally, then downgraded tier activates without auto-renewal conflicts

#### 5. Clean User Interface
- **Pending Subscription Filtering**: `filterPendingMembershipsFromDisplay` hides pending subscriptions from user-facing subscription page
- **Button Management**: `removeUpgradeButtonsForPendingMemberships` removes upgrade buttons for pending memberships
- **Benefit**: Reduces user confusion by hiding complex subscription states

### Technical Implementation

#### New Methods Added to Membership Class:
- `customizeProrationMessage()` - Hides proration messages for downgrades
- `addCustomDowngradeMessage()` - Shows clear downgrade timing information
- `customizeExpirationDateDisplay()` - Corrects expiration date display for pending memberships
- `handleAutoRenewalForDowngrades()` - Manages auto-renewal conflicts
- `filterPendingMembershipsFromDisplay()` - Hides pending subscriptions from UI
- `removeUpgradeButtonsForPendingMemberships()` - Removes confusing upgrade buttons

#### WordPress Hooks Used:
- `rcp_registration_prorate_message` - Customize proration messages
- `rcp_before_subscription_form_fields` - Add custom downgrade messages
- `rcp_membership_expiration_date` - Customize expiration date display
- `rcp_membership_post_activate` - Handle auto-renewal for downgrades
- `rcp_customer_get_memberships` - Filter pending memberships from display
- `rcp_subscription_details_actions` - Remove upgrade buttons for pending memberships

### Testing
Comprehensive test suite created in `SubscriptionDowngradeImprovementsTest.php` covering:
- Free membership logic consistency
- Proration message handling for upgrades vs downgrades
- Custom downgrade message display
- Expiration date customization for pending memberships
- Auto-renewal handling
- UI filtering of pending subscriptions
- Downgrade detection logic across all tier combinations

### Solution
Added early intervention using `rcp_membership_disable_prorate_credit` filter:
- **Early Hook**: `disableProrationCreditForDowngrades()` returns `true` to disable calculation entirely
- **Backup Filter**: Existing `disableProrationForDowngrades()` still applies for edge cases
- **Result**: Downgrades now show $0 proration in registration form and charge $0

### Technical Details
- Hook: `rcp_membership_disable_prorate_credit` (priority 10)
- Method: `disableProrationCreditForDowngrades($disable_credit, $membership)`
- Logic: Returns `true` for downgrades, `false` for upgrades/same-tier
- Testing: Added comprehensive test cases in `ProrationTest.php`

## 🧪 Testing
- **7 Test Files**: 62 tests, 400+ assertions - ALL PASSING ✅
- **Mock Data Only**: No secrets or wp-config data in tests (git-safe)
- **Run Tests**: `vendor/bin/phpunit` (~0.07 seconds)
- **Proration Tests**: Verify downgrade/upgrade proration behavior

## 🔄 Migration Pattern
```php
// OLD: dtc_get_current_user_customer()
// NEW: Membership::getCurrentUserCustomer()
// Add: use DynastyTradeCalculator\Membership;
```
