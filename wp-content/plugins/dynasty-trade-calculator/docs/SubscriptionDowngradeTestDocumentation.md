# Subscription Downgrade Test Documentation

## Overview
This document provides comprehensive testing guidelines for the Dynasty Trade Calculator subscription downgrade improvements implemented in June 2025.

## Test Environment Setup

### Prerequisites
- WordPress test environment with RCP plugin active
- Dynasty Trade Calculator plugin installed
- PHPUnit testing framework
- Mock data for various membership levels and subscription types

### Configuration Constants Required
```php
DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE = [
    ['membership_level_id' => 8, 'rotogpt_subscription_type' => 'free'],
    ['membership_level_id' => 7, 'rotogpt_subscription_type' => 'standard_50'],
    ['membership_level_id' => 9, 'rotogpt_subscription_type' => 'standard_100'],
    ['membership_level_id' => 12, 'rotogpt_subscription_type' => 'standard_200'],
    ['membership_level_id' => 5, 'rotogpt_subscription_type' => 'admin']
];
DTC_ROTOGPT_TEST_DRIVE_SUBSCRIPTION = 'free';
```

## Test Categories

### 1. Free Membership Logic Tests

#### Test Case: Unmapped Membership Levels
**Objective**: Verify that membership levels not in config are treated as free
**Steps**:
1. Create membership with level ID not in `DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE`
2. Call `getRotoGptSubscription()` method
3. Verify result equals 'free'

**Expected Result**: All unmapped membership levels return 'free' subscription type

#### Test Case: Mapped Membership Levels
**Objective**: Verify that mapped membership levels return correct subscription types
**Steps**:
1. Test each membership level ID in config (8, 7, 9, 12, 5)
2. Call `getRotoGptSubscription()` for each
3. Verify correct subscription type returned

**Expected Results**:
- Level 8 → 'free'
- Level 7 → 'standard_50'
- Level 9 → 'standard_100'
- Level 12 → 'standard_200'
- Level 5 → 'admin'

### 2. Proration Message Tests

#### Test Case: Downgrade Proration Message Hiding
**Objective**: Verify proration messages are hidden for downgrades
**Steps**:
1. Mock downgrade registration scenario
2. Call `customizeProrationMessage()` with sample proration message
3. Verify empty string returned

**Expected Result**: Proration message completely hidden for downgrades

#### Test Case: Upgrade Proration Message Preservation
**Objective**: Verify proration messages are kept for upgrades
**Steps**:
1. Mock upgrade registration scenario
2. Call `customizeProrationMessage()` with sample proration message
3. Verify original message returned unchanged

**Expected Result**: Original proration message preserved for upgrades

#### Test Case: Custom Downgrade Message Display
**Objective**: Verify custom downgrade message is shown
**Steps**:
1. Mock downgrade registration with active membership having expiration date
2. Call `addCustomDowngradeMessage()`
3. Capture output and verify content

**Expected Result**: Message contains "Downgrade Information" and explains activation timing

### 3. Expiration Date Display Tests

#### Test Case: Pending Membership Expiration Display
**Objective**: Verify correct expiration date display for pending memberships
**Steps**:
1. Create pending membership with customer having active membership
2. Call `customizeExpirationDateDisplay()` with original date
3. Verify customized message returned

**Expected Result**: Message shows "Activates [date] (after current tier expires)"

#### Test Case: Non-Pending Membership Expiration Display
**Objective**: Verify normal expiration dates unchanged for non-pending memberships
**Steps**:
1. Create active membership
2. Call `customizeExpirationDateDisplay()` with original date
3. Verify original date returned unchanged

**Expected Result**: Original expiration date preserved for active memberships

### 4. Auto-Renewal Handling Tests

#### Test Case: Downgrade Auto-Renewal Disabling
**Objective**: Verify auto-renewal disabled on old membership during downgrade activation
**Steps**:
1. Create old membership with auto-renewal enabled (standard_200)
2. Create new membership as downgrade (standard_100)
3. Call `handleAutoRenewalForDowngrades()`
4. Verify `set_recurring(false)` called on old membership

**Expected Result**: Auto-renewal disabled on old membership for downgrades

#### Test Case: Upgrade Auto-Renewal Preservation
**Objective**: Verify auto-renewal unchanged for upgrades
**Steps**:
1. Create old membership (standard_50)
2. Create new membership as upgrade (standard_100)
3. Call `handleAutoRenewalForDowngrades()`
4. Verify no changes to auto-renewal settings

**Expected Result**: Auto-renewal settings unchanged for upgrades

### 5. User Interface Filtering Tests

#### Test Case: Pending Membership Filtering
**Objective**: Verify pending memberships hidden from subscription page
**Steps**:
1. Create array with active and pending memberships
2. Mock subscription page context
3. Call `filterPendingMembershipsFromDisplay()`
4. Verify only active memberships returned

**Expected Result**: Pending memberships filtered out, active memberships preserved

#### Test Case: Admin Page Filtering Bypass
**Objective**: Verify filtering bypassed in admin context
**Steps**:
1. Mock admin context
2. Create array with active and pending memberships
3. Call `filterPendingMembershipsFromDisplay()`
4. Verify all memberships returned unchanged

**Expected Result**: No filtering applied in admin context

#### Test Case: Upgrade Button Removal
**Objective**: Verify upgrade buttons removed for pending memberships
**Steps**:
1. Create pending membership
2. Create action links including upgrade button
3. Call `removeUpgradeButtonsForPendingMemberships()`
4. Verify upgrade button removed, other buttons preserved

**Expected Result**: Upgrade buttons removed, other action buttons remain

### 6. Integration Tests

#### Test Case: Complete Downgrade Flow
**Objective**: Test entire downgrade process from start to finish
**Steps**:
1. User with active $9.99 membership downgrades to $4.99
2. Verify proration message hidden during registration
3. Verify custom downgrade message displayed
4. Verify pending membership created with correct expiration display
5. Verify pending membership hidden from subscription page
6. Simulate membership expiration and activation
7. Verify auto-renewal disabled on old membership
8. Verify RotoGPT subscription updated correctly

**Expected Result**: Complete downgrade flow works seamlessly with all improvements

#### Test Case: Multiple Tier Downgrade Scenarios
**Objective**: Test all possible downgrade combinations
**Test Matrix**:
- $9.99 → $6.99 (standard_200 → standard_100)
- $9.99 → $4.99 (standard_200 → standard_50)
- $9.99 → Free (standard_200 → free)
- $6.99 → $4.99 (standard_100 → standard_50)
- $6.99 → Free (standard_100 → free)
- $4.99 → Free (standard_50 → free)

**Expected Result**: All downgrade combinations work correctly with proper messaging and timing

## Manual Testing Checklist

### User Experience Tests
- [ ] Downgrade registration form shows no proration amount
- [ ] Downgrade registration form shows clear activation timing message
- [ ] Subscription page hides pending memberships
- [ ] Subscription page shows correct expiration dates
- [ ] No upgrade buttons visible for pending memberships
- [ ] Auto-renewal properly managed during downgrades

### Edge Cases
- [ ] User with no expiration date on current membership
- [ ] User with multiple active memberships
- [ ] User with expired membership attempting downgrade
- [ ] Downgrade to unmapped membership level
- [ ] Concurrent upgrade and downgrade attempts

## Performance Considerations

### Database Queries
- Monitor additional queries from new filters and hooks
- Ensure efficient membership status checking
- Verify customer membership retrieval performance

### Caching
- Test with WordPress object caching enabled
- Verify membership data consistency across requests
- Check for any caching conflicts with RCP

## Troubleshooting Guide

### Common Issues
1. **Proration messages still showing**: Check filter priority and registration type detection
2. **Pending memberships visible**: Verify subscription page detection logic
3. **Auto-renewal not disabled**: Check membership upgrade detection and RCP version compatibility
4. **Custom messages not displaying**: Verify hook timing and registration object availability

### Debug Information
- Enable DTC debug logging to trace subscription operations
- Check WordPress debug log for filter execution
- Monitor RCP membership status transitions
- Verify RotoGPT API integration logs

## Acceptance Criteria Summary

✅ **Free Membership Logic**: Unmapped levels treated as free consistently
✅ **Proration Messages**: Hidden for downgrades, preserved for upgrades  
✅ **Expiration Dates**: Show correct activation timing for pending downgrades
✅ **Auto-Renewal**: Properly managed to prevent conflicts
✅ **User Interface**: Clean display without confusing pending subscriptions
✅ **Comprehensive Testing**: Full test coverage for all scenarios

## Test Execution Commands

```bash
# Run specific test class
phpunit wp-content/plugins/dynasty-trade-calculator/tests/SubscriptionDowngradeImprovementsTest.php

# Run all subscription tests
phpunit wp-content/plugins/dynasty-trade-calculator/tests/ --filter="Subscription"

# Run with coverage report
phpunit --coverage-html coverage/ wp-content/plugins/dynasty-trade-calculator/tests/
```

## Maintenance Notes

- Update tests when new membership levels added to config
- Review test scenarios when RCP plugin updates
- Monitor for WordPress core changes affecting subscription display
- Verify compatibility with future RotoGPT API changes
