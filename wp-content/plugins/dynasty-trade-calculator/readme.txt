=== Dynasty Trade Calculator ===
Contributors: dtc
Tags: dynasty trade calculator
Requires at least: 6.0
Tested up to: 6.8
Requires PHP: 7.4
Stable tag: 2.5.3
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Dynasty trade calculator and more!

== Description ==
Dynasty trade calculator plugin

== Installation ==
Use WordPress plugin uploader feature

== Frequently Asked Questions ==

= Does it work with all WordPress themes? =

Yes, it works with all WordPress themes.

== Screenshots ==
1. Screen Capture 1
2. Screen Capture 2

== Changelog ==

= 2.5.3 - 28/06/2025 =
- Fixed: Downgrade proration | Turned off proration for downgrades
- Few minor bug fixes

= 2.5.2 - 26/06/2025 =
- Fatal error fix
- Few minor bug fixes

= 2.5.1 - 17/06/2025 =
- Fixed: REST API | Subscription management endpoints
- Fixed: RotoGPT Integration | Enhanced subscription handling for membership changes
- Fixed: Membership Management | Improved upgrade/downgrade functionality
- Fixed: API Security | Enhanced user input validation and response structure
- Fixed: Calculator | Various debugging improvements and bug fixes
- Few minor bug fixes

= 2.5.0 - 14/06/2025 =
- Added: REST API | Subscription management endpoints
- Added: RotoGPT Integration | Enhanced subscription handling for membership changes
- Added: Membership Management | Improved upgrade/downgrade functionality
- Added: API Security | Enhanced user input validation and response structure
- Fixed: Calculator | Various debugging improvements and bug fixes
- Few minor bug fixes

= 2.4.3 - 10/06/2025 =
- Fixed: League Integration | Tab switch issue on logout
- Fixed: Calculator | Team size 8 causes NaN
- Few minor bug fixes 

= 2.4.2 - 27/05/2025 =
- Fixed: League Integration | Several bug fixes
- Fixed: Player Dashboard | Archive feature pagination issues
- Few minor bug fixes 

= 2.4.1 - 20/05/2025 =
- Fixed: League Integration | Several bug fixes
- Few minor bug fixes 

= 2.4.0 - 07/05/2025 =
- Added: Player Dashboard | Archive feature
- Added: Player Dashboard | Badge lookup
- Few minor bug fixes 

= 2.3.0 - 07/10/2024 =
- Fixed: Alignment issues on various files
- Fixed: Debug log errors
- Few minor bug fixes 

= 1.0.0 = 
* Initial version and commit

== Upgrade Notice ==

= 1.0.0 = 
* Initial version and commit