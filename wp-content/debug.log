
[28-Jun-2025 07:28:47 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:47 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:28:47 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:47 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:47 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:48 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:48 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/subscription
[28-Jun-2025 07:28:48 UTC] DTC DEBUG: Current page - ID: 31442, URL: /subscription/, RCP subscription page ID: 0
[28-Jun-2025 07:28:48 UTC] DTC DEBUG: This appears to be a subscription page based on URL
[28-Jun-2025 07:28:48 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:48 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:48 UTC] DTC DEBUG: On subscription page (ID: 31442, URL: /subscription/), enqueuing assets to hide pending memberships
[28-Jun-2025 07:28:48 UTC] DTC DEBUG: Starting auto-renewal configuration audit
[28-Jun-2025 07:28:48 UTC] DTC DEBUG: Global auto-renewal behavior: 3 (1=always, 2=never, 3=customer choice)
[28-Jun-2025 07:28:48 UTC] DTC DEBUG: Level 7 (Calculator with ChatDTC (50 Credits)) - Price: $4.99, Free: no, Lifetime: no
[28-Jun-2025 07:28:48 UTC] DTC DEBUG: Level 7 should auto-renew: yes
[28-Jun-2025 07:28:48 UTC] DTC DEBUG: Level 9 (Calculator with ChatDTC (100 Credits)) - Price: $6.99, Free: no, Lifetime: no
[28-Jun-2025 07:28:48 UTC] DTC DEBUG: Level 9 should auto-renew: yes
[28-Jun-2025 07:28:48 UTC] DTC DEBUG: Level 12 (Calculator with ChatDTC (Unlimited Credits)) - Price: $9.99, Free: no, Lifetime: no
[28-Jun-2025 07:28:48 UTC] DTC DEBUG: Level 12 should auto-renew: yes
[28-Jun-2025 07:28:48 UTC] DTC DEBUG: Membership 34623 (Level: 7, Status: active) - Auto-renew: yes
[28-Jun-2025 07:28:48 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:48 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:28:48 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:28:49 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:49 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:49 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:49 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:28:51 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:28:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/sw.js
[28-Jun-2025 07:28:51 UTC] DTC DEBUG: Current page - ID: , URL: /sw.js, RCP subscription page ID: 0
[28-Jun-2025 07:28:51 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/manifest.json
[28-Jun-2025 07:28:51 UTC] DTC DEBUG: Current page - ID: , URL: /manifest.json, RCP subscription page ID: 0
[28-Jun-2025 07:28:51 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:51 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:28:51 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:28:52 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:28:52 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:28:52 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:52 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:52 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:28:52 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:15 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:31:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:15 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/subscription
[28-Jun-2025 07:31:15 UTC] DTC DEBUG: Current page - ID: 31442, URL: /subscription/, RCP subscription page ID: 0
[28-Jun-2025 07:31:15 UTC] DTC DEBUG: This appears to be a subscription page based on URL
[28-Jun-2025 07:31:15 UTC] DTC DEBUG: On subscription page (ID: 31442, URL: /subscription/), enqueuing assets to hide pending memberships
[28-Jun-2025 07:31:15 UTC] DTC DEBUG: Starting auto-renewal configuration audit
[28-Jun-2025 07:31:15 UTC] DTC DEBUG: Global auto-renewal behavior: 3 (1=always, 2=never, 3=customer choice)
[28-Jun-2025 07:31:15 UTC] DTC DEBUG: Level 7 (Calculator with ChatDTC (50 Credits)) - Price: $4.99, Free: no, Lifetime: no
[28-Jun-2025 07:31:15 UTC] DTC DEBUG: Level 7 should auto-renew: yes
[28-Jun-2025 07:31:15 UTC] DTC DEBUG: Level 9 (Calculator with ChatDTC (100 Credits)) - Price: $6.99, Free: no, Lifetime: no
[28-Jun-2025 07:31:15 UTC] DTC DEBUG: Level 9 should auto-renew: yes
[28-Jun-2025 07:31:15 UTC] DTC DEBUG: Level 12 (Calculator with ChatDTC (Unlimited Credits)) - Price: $9.99, Free: no, Lifetime: no
[28-Jun-2025 07:31:15 UTC] DTC DEBUG: Level 12 should auto-renew: yes
[28-Jun-2025 07:31:15 UTC] DTC DEBUG: Membership 34624 (Level: 10, Status: pending) - Auto-renew: yes
[28-Jun-2025 07:31:15 UTC] DTC DEBUG: Membership 34620 (Level: 10, Status: pending) - Auto-renew: yes
[28-Jun-2025 07:31:15 UTC] DTC DEBUG: Membership 34619 (Level: 7, Status: active) - Auto-renew: yes
[28-Jun-2025 07:31:15 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:31:16 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:31:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:17 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:31:17 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:31:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:17 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:31:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:18 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/images/icons/icon-192x192.png
[28-Jun-2025 07:31:18 UTC] DTC DEBUG: Current page - ID: 11896, URL: /images/icons/icon-192x192.png, RCP subscription page ID: 0
[28-Jun-2025 07:31:18 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/images/icons/icon-192x192.png
[28-Jun-2025 07:31:18 UTC] DTC DEBUG: Current page - ID: 11896, URL: /images/icons/icon-192x192.png, RCP subscription page ID: 0
[28-Jun-2025 07:31:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:18 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test
[28-Jun-2025 07:31:18 UTC] DTC DEBUG: Current page - ID: 11896, URL: /, RCP subscription page ID: 0
[28-Jun-2025 07:31:18 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:31:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:18 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/sw.js
[28-Jun-2025 07:31:18 UTC] DTC DEBUG: Current page - ID: , URL: /sw.js, RCP subscription page ID: 0
[28-Jun-2025 07:31:18 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:31:18 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:31:19 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:31:19 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:31:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:21 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:31:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:21 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/subscription
[28-Jun-2025 07:31:21 UTC] DTC DEBUG: Current page - ID: 31442, URL: /subscription/, RCP subscription page ID: 0
[28-Jun-2025 07:31:21 UTC] DTC DEBUG: This appears to be a subscription page based on URL
[28-Jun-2025 07:31:21 UTC] DTC DEBUG: On subscription page (ID: 31442, URL: /subscription/), enqueuing assets to hide pending memberships
[28-Jun-2025 07:31:21 UTC] DTC DEBUG: Starting auto-renewal configuration audit
[28-Jun-2025 07:31:21 UTC] DTC DEBUG: Global auto-renewal behavior: 3 (1=always, 2=never, 3=customer choice)
[28-Jun-2025 07:31:21 UTC] DTC DEBUG: Level 7 (Calculator with ChatDTC (50 Credits)) - Price: $4.99, Free: no, Lifetime: no
[28-Jun-2025 07:31:21 UTC] DTC DEBUG: Level 7 should auto-renew: yes
[28-Jun-2025 07:31:21 UTC] DTC DEBUG: Level 9 (Calculator with ChatDTC (100 Credits)) - Price: $6.99, Free: no, Lifetime: no
[28-Jun-2025 07:31:21 UTC] DTC DEBUG: Level 9 should auto-renew: yes
[28-Jun-2025 07:31:21 UTC] DTC DEBUG: Level 12 (Calculator with ChatDTC (Unlimited Credits)) - Price: $9.99, Free: no, Lifetime: no
[28-Jun-2025 07:31:21 UTC] DTC DEBUG: Level 12 should auto-renew: yes
[28-Jun-2025 07:31:21 UTC] DTC DEBUG: Membership 34624 (Level: 10, Status: pending) - Auto-renew: yes
[28-Jun-2025 07:31:21 UTC] DTC DEBUG: Membership 34620 (Level: 10, Status: pending) - Auto-renew: yes
[28-Jun-2025 07:31:21 UTC] DTC DEBUG: Membership 34619 (Level: 7, Status: active) - Auto-renew: yes
[28-Jun-2025 07:31:22 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:31:22 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:31:23 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:23 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:31:23 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:24 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/sw.js
[28-Jun-2025 07:31:24 UTC] DTC DEBUG: Current page - ID: , URL: /sw.js, RCP subscription page ID: 0
[28-Jun-2025 07:31:24 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:31:24 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:31:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:25 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:31:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:25 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/subscription
[28-Jun-2025 07:31:25 UTC] DTC DEBUG: Current page - ID: 31442, URL: /subscription/, RCP subscription page ID: 0
[28-Jun-2025 07:31:25 UTC] DTC DEBUG: This appears to be a subscription page based on URL
[28-Jun-2025 07:31:25 UTC] DTC DEBUG: On subscription page (ID: 31442, URL: /subscription/), enqueuing assets to hide pending memberships
[28-Jun-2025 07:31:25 UTC] DTC DEBUG: Starting auto-renewal configuration audit
[28-Jun-2025 07:31:25 UTC] DTC DEBUG: Global auto-renewal behavior: 3 (1=always, 2=never, 3=customer choice)
[28-Jun-2025 07:31:25 UTC] DTC DEBUG: Level 7 (Calculator with ChatDTC (50 Credits)) - Price: $4.99, Free: no, Lifetime: no
[28-Jun-2025 07:31:25 UTC] DTC DEBUG: Level 7 should auto-renew: yes
[28-Jun-2025 07:31:25 UTC] DTC DEBUG: Level 9 (Calculator with ChatDTC (100 Credits)) - Price: $6.99, Free: no, Lifetime: no
[28-Jun-2025 07:31:25 UTC] DTC DEBUG: Level 9 should auto-renew: yes
[28-Jun-2025 07:31:25 UTC] DTC DEBUG: Level 12 (Calculator with ChatDTC (Unlimited Credits)) - Price: $9.99, Free: no, Lifetime: no
[28-Jun-2025 07:31:25 UTC] DTC DEBUG: Level 12 should auto-renew: yes
[28-Jun-2025 07:31:25 UTC] DTC DEBUG: Membership 34624 (Level: 10, Status: pending) - Auto-renew: yes
[28-Jun-2025 07:31:25 UTC] DTC DEBUG: Membership 34620 (Level: 10, Status: pending) - Auto-renew: yes
[28-Jun-2025 07:31:25 UTC] DTC DEBUG: Membership 34619 (Level: 7, Status: active) - Auto-renew: yes
[28-Jun-2025 07:31:25 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:31:25 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:31:26 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:26 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:31:26 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:27 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:27 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:31:27 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:27 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test
[28-Jun-2025 07:31:27 UTC] DTC DEBUG: Current page - ID: 11896, URL: /, RCP subscription page ID: 0
[28-Jun-2025 07:31:27 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:27 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:31:27 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test
[28-Jun-2025 07:31:27 UTC] DTC DEBUG: Current page - ID: 11896, URL: /, RCP subscription page ID: 0
[28-Jun-2025 07:31:27 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:31:27 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:31:27 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/sw.js
[28-Jun-2025 07:31:27 UTC] DTC DEBUG: Current page - ID: , URL: /sw.js, RCP subscription page ID: 0
[28-Jun-2025 07:31:27 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:31:27 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:31:27 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:31:27 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:31:28 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:32:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:36 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:32:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:36 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:36 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/subscription
[28-Jun-2025 07:32:36 UTC] DTC DEBUG: Current page - ID: 31442, URL: /subscription/, RCP subscription page ID: 0
[28-Jun-2025 07:32:36 UTC] DTC DEBUG: This appears to be a subscription page based on URL
[28-Jun-2025 07:32:36 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:36 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:36 UTC] DTC DEBUG: On subscription page (ID: 31442, URL: /subscription/), enqueuing assets to hide pending memberships
[28-Jun-2025 07:32:36 UTC] DTC DEBUG: Starting auto-renewal configuration audit
[28-Jun-2025 07:32:36 UTC] DTC DEBUG: Global auto-renewal behavior: 3 (1=always, 2=never, 3=customer choice)
[28-Jun-2025 07:32:36 UTC] DTC DEBUG: Level 7 (Calculator with ChatDTC (50 Credits)) - Price: $4.99, Free: no, Lifetime: no
[28-Jun-2025 07:32:36 UTC] DTC DEBUG: Level 7 should auto-renew: yes
[28-Jun-2025 07:32:36 UTC] DTC DEBUG: Level 9 (Calculator with ChatDTC (100 Credits)) - Price: $6.99, Free: no, Lifetime: no
[28-Jun-2025 07:32:36 UTC] DTC DEBUG: Level 9 should auto-renew: yes
[28-Jun-2025 07:32:36 UTC] DTC DEBUG: Level 12 (Calculator with ChatDTC (Unlimited Credits)) - Price: $9.99, Free: no, Lifetime: no
[28-Jun-2025 07:32:36 UTC] DTC DEBUG: Level 12 should auto-renew: yes
[28-Jun-2025 07:32:36 UTC] DTC DEBUG: Membership 34623 (Level: 7, Status: active) - Auto-renew: yes
[28-Jun-2025 07:32:36 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:37 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:32:37 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:32:38 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:38 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:38 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:38 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:39 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:32:39 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:32:39 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:32:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/manifest.json
[28-Jun-2025 07:32:40 UTC] DTC DEBUG: Current page - ID: , URL: /manifest.json, RCP subscription page ID: 0
[28-Jun-2025 07:32:40 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/images/icons/icon-192x192.png
[28-Jun-2025 07:32:40 UTC] DTC DEBUG: Current page - ID: 11896, URL: /images/icons/icon-192x192.png, RCP subscription page ID: 0
[28-Jun-2025 07:32:40 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/sw.js
[28-Jun-2025 07:32:40 UTC] DTC DEBUG: Current page - ID: , URL: /sw.js, RCP subscription page ID: 0
[28-Jun-2025 07:32:40 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:32:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:32:40 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:32:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test
[28-Jun-2025 07:32:40 UTC] DTC DEBUG: Current page - ID: 11896, URL: /, RCP subscription page ID: 0
[28-Jun-2025 07:32:40 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:40 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:32:40 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:32:40 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:32:41 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:32:41 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:41 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:41 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:41 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:41 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:41 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:41 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:41 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:48 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:32:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:48 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:48 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/manifest.json
[28-Jun-2025 07:32:48 UTC] DTC DEBUG: Current page - ID: , URL: /manifest.json, RCP subscription page ID: 0
[28-Jun-2025 07:32:48 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:48 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:48 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:48 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:32:49 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:32:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:49 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:32:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:49 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:49 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/sm/b0ce608ffc029736e9ac80a8dd6a7db2da8e1d45d2dcfc92043deb2214aa30d8.map
[28-Jun-2025 07:32:49 UTC] DTC DEBUG: Current page - ID: , URL: /sm/b0ce608ffc029736e9ac80a8dd6a7db2da8e1d45d2dcfc92043deb2214aa30d8.map, RCP subscription page ID: 0
[28-Jun-2025 07:32:49 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:49 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:49 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:50 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:50 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:50 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:50 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:32:50 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:32:50 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:03 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:03 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:03 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:03 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/subscription
[28-Jun-2025 07:33:03 UTC] DTC DEBUG: Current page - ID: 31442, URL: /subscription/, RCP subscription page ID: 0
[28-Jun-2025 07:33:03 UTC] DTC DEBUG: This appears to be a subscription page based on URL
[28-Jun-2025 07:33:03 UTC] DTC DEBUG: On subscription page (ID: 31442, URL: /subscription/), enqueuing assets to hide pending memberships
[28-Jun-2025 07:33:03 UTC] DTC DEBUG: Starting auto-renewal configuration audit
[28-Jun-2025 07:33:03 UTC] DTC DEBUG: Global auto-renewal behavior: 3 (1=always, 2=never, 3=customer choice)
[28-Jun-2025 07:33:03 UTC] DTC DEBUG: Level 7 (Calculator with ChatDTC (50 Credits)) - Price: $4.99, Free: no, Lifetime: no
[28-Jun-2025 07:33:03 UTC] DTC DEBUG: Level 7 should auto-renew: yes
[28-Jun-2025 07:33:03 UTC] DTC DEBUG: Level 9 (Calculator with ChatDTC (100 Credits)) - Price: $6.99, Free: no, Lifetime: no
[28-Jun-2025 07:33:03 UTC] DTC DEBUG: Level 9 should auto-renew: yes
[28-Jun-2025 07:33:03 UTC] DTC DEBUG: Level 12 (Calculator with ChatDTC (Unlimited Credits)) - Price: $9.99, Free: no, Lifetime: no
[28-Jun-2025 07:33:03 UTC] DTC DEBUG: Level 12 should auto-renew: yes
[28-Jun-2025 07:33:03 UTC] DTC DEBUG: Membership 34624 (Level: 10, Status: pending) - Auto-renew: yes
[28-Jun-2025 07:33:03 UTC] DTC DEBUG: Membership 34620 (Level: 10, Status: pending) - Auto-renew: yes
[28-Jun-2025 07:33:03 UTC] DTC DEBUG: Membership 34619 (Level: 7, Status: active) - Auto-renew: yes
[28-Jun-2025 07:33:03 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:04 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:05 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:05 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:05 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:05 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test
[28-Jun-2025 07:33:05 UTC] DTC DEBUG: Current page - ID: 11896, URL: /, RCP subscription page ID: 0
[28-Jun-2025 07:33:05 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:05 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:05 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:06 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:06 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/sw.js
[28-Jun-2025 07:33:06 UTC] DTC DEBUG: Current page - ID: , URL: /sw.js, RCP subscription page ID: 0
[28-Jun-2025 07:33:06 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:06 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:06 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:18 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:18 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:18 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/signup
[28-Jun-2025 07:33:18 UTC] DTC DEBUG: This is a signup page
[28-Jun-2025 07:33:18 UTC] DTC DEBUG: RCP registration object exists
[28-Jun-2025 07:33:18 UTC] DTC DEBUG: Current page - ID: 31441, URL: /signup/?registration_type=upgrade&membership_id=34623, RCP subscription page ID: 0
[28-Jun-2025 07:33:18 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:18 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:18 UTC] DTC DEBUG: Template redirect on signup page
[28-Jun-2025 07:33:18 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:18 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:19 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:19 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:19 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:20 UTC] PHP Deprecated:  Function RCP_Levels::get_level is <strong>deprecated</strong> since version 3.4! Use rcp_get_membership_level instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:20 UTC] PHP Deprecated:  Function RCP_Levels::get_level is <strong>deprecated</strong> since version 3.4! Use rcp_get_membership_level instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:20 UTC] PHP Deprecated:  Function RCP_Levels::get_level is <strong>deprecated</strong> since version 3.4! Use rcp_get_membership_level instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:20 UTC] PHP Deprecated:  Function RCP_Levels::get_level is <strong>deprecated</strong> since version 3.4! Use rcp_get_membership_level instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:20 UTC] PHP Deprecated:  Function RCP_Levels::get_level is <strong>deprecated</strong> since version 3.4! Use rcp_get_membership_level instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:20 UTC] PHP Deprecated:  Function RCP_Levels::get_level is <strong>deprecated</strong> since version 3.4! Use rcp_get_membership_level instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:20 UTC] PHP Deprecated:  Function RCP_Levels::get_level is <strong>deprecated</strong> since version 3.4! Use rcp_get_membership_level instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:20 UTC] PHP Deprecated:  Function RCP_Levels::get_level is <strong>deprecated</strong> since version 3.4! Use rcp_get_membership_level instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: rcp_before_register_form hook fired - ID: , Atts: Array
(
    [id] => 
    [ids] => 9,10,11,12
    [registered_message] => You are already registered and have an active subscription.
    [logged_out_header] => Register New Account
    [logged_in_header] => Upgrade or Renew Your Membership
)

[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Registration object exists in before_register_form
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: debugRegistrationDetails() called
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: URL Parameters - registration_type: upgrade, membership_id: 34623
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: User logged in: yes (ID: 25888)
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: User has 1 memberships
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Membership ID: 34623, Level: 7, Status: active
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Target membership exists - ID: 34623, User: 25888, Level: 7, Status: active
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Registration Details - Type: upgrade, Target Level: 0
[28-Jun-2025 07:33:20 UTC] [2025-06-28 07:33:20] [DTC-INFO] DTC Debug: Registration Details - Type: upgrade, Target Level: 0
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Current Membership - Level: 7, Status: active
[28-Jun-2025 07:33:20 UTC] [2025-06-28 07:33:20] [DTC-INFO] DTC Debug: Current Membership - Level: 7, Status: active
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: isCurrentRegistrationADowngrade() called
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Target level ID from registration: 0
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Current membership from registration exists: yes
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: No target level from registration, checking URL parameters and form context
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: URL indicates upgrade intent, treating as potential downgrade scenario
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Is Downgrade: YES
[28-Jun-2025 07:33:20 UTC] [2025-06-28 07:33:20] [DTC-INFO] DTC Debug: Is Downgrade: YES
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: removeRcpProrationMessageForDowngrades() called
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: isCurrentRegistrationADowngrade() called
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Target level ID from registration: 0
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Current membership from registration exists: yes
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: No target level from registration, checking URL parameters and form context
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: URL indicates upgrade intent, treating as potential downgrade scenario
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Removed RCP proration message action for downgrade
[28-Jun-2025 07:33:20 UTC] [2025-06-28 07:33:20] [DTC-INFO] DTC Proration: Removed RCP proration message action for downgrade
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: debugRegistrationDetails() called
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: URL Parameters - registration_type: upgrade, membership_id: 34623
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: User logged in: yes (ID: 25888)
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: User has 1 memberships
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Membership ID: 34623, Level: 7, Status: active
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Target membership exists - ID: 34623, User: 25888, Level: 7, Status: active
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Registration Details - Type: upgrade, Target Level: 0
[28-Jun-2025 07:33:20 UTC] [2025-06-28 07:33:20] [DTC-INFO] DTC Debug: Registration Details - Type: upgrade, Target Level: 0
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Current Membership - Level: 7, Status: active
[28-Jun-2025 07:33:20 UTC] [2025-06-28 07:33:20] [DTC-INFO] DTC Debug: Current Membership - Level: 7, Status: active
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: isCurrentRegistrationADowngrade() called
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Target level ID from registration: 0
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Current membership from registration exists: yes
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: No target level from registration, checking URL parameters and form context
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: URL indicates upgrade intent, treating as potential downgrade scenario
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Is Downgrade: YES
[28-Jun-2025 07:33:20 UTC] [2025-06-28 07:33:20] [DTC-INFO] DTC Debug: Is Downgrade: YES
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: addCustomDowngradeMessage() called
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: isCurrentRegistrationADowngrade() called
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Target level ID from registration: 0
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Current membership from registration exists: yes
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: No target level from registration, checking URL parameters and form context
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: URL indicates upgrade intent, treating as potential downgrade scenario
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: This is a downgrade, showing custom message
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Displaying custom downgrade message
[28-Jun-2025 07:33:20 UTC] [2025-06-28 07:33:20] [DTC-INFO] DTC Downgrade: Displaying custom downgrade message
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: rcp_after_register_form hook fired - ID: , Atts: Array
(
    [id] => 
    [ids] => 9,10,11,12
    [registered_message] => You are already registered and have an active subscription.
    [logged_out_header] => Register New Account
    [logged_in_header] => Upgrade or Renew Your Membership
)

[28-Jun-2025 07:33:20 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:20 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:20 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affwp-paypal-payouts</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:21 UTC] [2025-06-28 07:33:21] [DTC-DEBUG] Membership level in getRotoGptSubscription: 7
[28-Jun-2025 07:33:21 UTC] [2025-06-28 07:33:21] [DTC-DEBUG] Found mapping for membership level 7: Array
(
    [membership_level_id] => 7
    [rotogpt_subscription_type] => standard_50
)

[28-Jun-2025 07:33:21 UTC] [2025-06-28 07:33:21] [DTC-DEBUG] Membership level in getRotoGptSubscription: 7
[28-Jun-2025 07:33:21 UTC] [2025-06-28 07:33:21] [DTC-DEBUG] Found mapping for membership level 7: Array
(
    [membership_level_id] => 7
    [rotogpt_subscription_type] => standard_50
)

[28-Jun-2025 07:33:21 UTC] [2025-06-28 07:33:21] [DTC-INFO] DTC Registration: Allowing proration fee for upgrade from standard_50 to standard_200
[28-Jun-2025 07:33:21 UTC] [2025-06-28 07:33:21] [DTC-DEBUG] Membership level in getRotoGptSubscription: 7
[28-Jun-2025 07:33:21 UTC] [2025-06-28 07:33:21] [DTC-DEBUG] Found mapping for membership level 7: Array
(
    [membership_level_id] => 7
    [rotogpt_subscription_type] => standard_50
)

[28-Jun-2025 07:33:21 UTC] [2025-06-28 07:33:21] [DTC-DEBUG] Membership level in getRotoGptSubscription: 7
[28-Jun-2025 07:33:21 UTC] [2025-06-28 07:33:21] [DTC-DEBUG] Found mapping for membership level 7: Array
(
    [membership_level_id] => 7
    [rotogpt_subscription_type] => standard_50
)

[28-Jun-2025 07:33:21 UTC] [2025-06-28 07:33:21] [DTC-INFO] DTC Registration: Allowing proration fee for upgrade from standard_50 to standard_200
[28-Jun-2025 07:33:21 UTC] DTC DEBUG: fixRenewalDateForDowngrades() called
[28-Jun-2025 07:33:21 UTC] DTC DEBUG: isCurrentRegistrationADowngrade() called
[28-Jun-2025 07:33:21 UTC] DTC DEBUG: Target level ID from registration: 12
[28-Jun-2025 07:33:21 UTC] DTC DEBUG: Current membership from registration exists: yes
[28-Jun-2025 07:33:21 UTC] [2025-06-28 07:33:21] [DTC-DEBUG] Membership level in getRotoGptSubscription: 7
[28-Jun-2025 07:33:21 UTC] [2025-06-28 07:33:21] [DTC-DEBUG] Found mapping for membership level 7: Array
(
    [membership_level_id] => 7
    [rotogpt_subscription_type] => standard_50
)

[28-Jun-2025 07:33:21 UTC] DTC DEBUG: Current subscription type: standard_50
[28-Jun-2025 07:33:21 UTC] DTC DEBUG: Target subscription type: standard_200
[28-Jun-2025 07:33:21 UTC] DTC DEBUG: Current index: 1, Target index: 3, Is downgrade: NO
[28-Jun-2025 07:33:21 UTC] [2025-06-28 07:33:21] [DTC-INFO] DTC Downgrade Detection: Current=standard_50 (index=1), Target=standard_200 (index=3), IsDowngrade=no
[28-Jun-2025 07:33:21 UTC] DTC DEBUG: Not a downgrade, keeping default renewal date
[28-Jun-2025 07:33:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:21 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:21 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/sw.js
[28-Jun-2025 07:33:22 UTC] DTC DEBUG: Current page - ID: , URL: /sw.js, RCP subscription page ID: 0
[28-Jun-2025 07:33:22 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/manifest.json
[28-Jun-2025 07:33:22 UTC] DTC DEBUG: Current page - ID: , URL: /manifest.json, RCP subscription page ID: 0
[28-Jun-2025 07:33:22 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affwp-paypal-payouts</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:22 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affwp-paypal-payouts</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:23 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:23 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:24 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:24 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:24 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:24 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:24 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:24 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affwp-paypal-payouts</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affwp-paypal-payouts</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:25 UTC] [2025-06-28 07:33:25] [DTC-DEBUG] Membership level in getRotoGptSubscription: 7
[28-Jun-2025 07:33:25 UTC] [2025-06-28 07:33:25] [DTC-DEBUG] Found mapping for membership level 7: Array
(
    [membership_level_id] => 7
    [rotogpt_subscription_type] => standard_50
)

[28-Jun-2025 07:33:25 UTC] [2025-06-28 07:33:25] [DTC-DEBUG] Membership level in getRotoGptSubscription: 7
[28-Jun-2025 07:33:25 UTC] [2025-06-28 07:33:25] [DTC-DEBUG] Found mapping for membership level 7: Array
(
    [membership_level_id] => 7
    [rotogpt_subscription_type] => standard_50
)

[28-Jun-2025 07:33:25 UTC] [2025-06-28 07:33:25] [DTC-INFO] DTC Registration: Blocking proration fee for downgrade from ChatDTC (standard_50) to regular DTC membership (level 10) - fee amount: -4.99
[28-Jun-2025 07:33:25 UTC] [2025-06-28 07:33:25] [DTC-DEBUG] Membership level in getRotoGptSubscription: 7
[28-Jun-2025 07:33:25 UTC] [2025-06-28 07:33:25] [DTC-DEBUG] Found mapping for membership level 7: Array
(
    [membership_level_id] => 7
    [rotogpt_subscription_type] => standard_50
)

[28-Jun-2025 07:33:25 UTC] [2025-06-28 07:33:25] [DTC-DEBUG] Membership level in getRotoGptSubscription: 7
[28-Jun-2025 07:33:25 UTC] [2025-06-28 07:33:25] [DTC-DEBUG] Found mapping for membership level 7: Array
(
    [membership_level_id] => 7
    [rotogpt_subscription_type] => standard_50
)

[28-Jun-2025 07:33:25 UTC] [2025-06-28 07:33:25] [DTC-INFO] DTC Registration: Blocking proration fee for downgrade from ChatDTC (standard_50) to regular DTC membership (level 10) - fee amount: -4.99
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 499
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 499
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 533
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 537
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 541
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 565
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 499
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 533
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 537
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 541
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 565
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 499
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 499
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/templates/register-total-details.php on line 81
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/templates/register-total-details.php on line 82
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Undefined array key "description" in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/templates/register-total-details.php on line 85
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 533
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 537
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 541
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 565
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 533
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 537
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 541
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 565
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 499
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 499
[28-Jun-2025 07:33:25 UTC] DTC DEBUG: fixRenewalDateForDowngrades() called
[28-Jun-2025 07:33:25 UTC] DTC DEBUG: isCurrentRegistrationADowngrade() called
[28-Jun-2025 07:33:25 UTC] DTC DEBUG: Target level ID from registration: 10
[28-Jun-2025 07:33:25 UTC] DTC DEBUG: Current membership from registration exists: yes
[28-Jun-2025 07:33:25 UTC] [2025-06-28 07:33:25] [DTC-DEBUG] Membership level in getRotoGptSubscription: 7
[28-Jun-2025 07:33:25 UTC] [2025-06-28 07:33:25] [DTC-DEBUG] Found mapping for membership level 7: Array
(
    [membership_level_id] => 7
    [rotogpt_subscription_type] => standard_50
)

[28-Jun-2025 07:33:25 UTC] DTC DEBUG: Current subscription type: standard_50
[28-Jun-2025 07:33:25 UTC] DTC DEBUG: Target subscription type: free
[28-Jun-2025 07:33:25 UTC] DTC DEBUG: Current index: 1, Target index: 0, Is downgrade: YES
[28-Jun-2025 07:33:25 UTC] [2025-06-28 07:33:25] [DTC-INFO] DTC Downgrade Detection: Current=standard_50 (index=1), Target=free (index=0), IsDowngrade=yes
[28-Jun-2025 07:33:25 UTC] DTC DEBUG: This is a downgrade, fixing renewal date display
[28-Jun-2025 07:33:25 UTC] DTC DEBUG: Displaying custom renewal date: July 28, 2025 (formatted: July 28, 2025)
[28-Jun-2025 07:33:25 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 499
[28-Jun-2025 07:33:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:30 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affwp-paypal-payouts</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:30 UTC] [2025-06-28 07:33:30] [DTC-DEBUG] Membership level in getRotoGptSubscription: 7
[28-Jun-2025 07:33:30 UTC] [2025-06-28 07:33:30] [DTC-DEBUG] Found mapping for membership level 7: Array
(
    [membership_level_id] => 7
    [rotogpt_subscription_type] => standard_50
)

[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-DEBUG] Membership level in getRotoGptSubscription: 7
[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-DEBUG] Found mapping for membership level 7: Array
(
    [membership_level_id] => 7
    [rotogpt_subscription_type] => standard_50
)

[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-INFO] DTC Registration: Blocking proration fee for downgrade from ChatDTC (standard_50) to regular DTC membership (level 10) - fee amount: -4.99
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 533
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 537
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 541
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 565
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 499
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 533
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 537
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 541
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 565
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 533
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 537
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 541
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 565
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 533
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 537
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 541
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 565
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 499
[28-Jun-2025 07:33:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:31 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affwp-paypal-payouts</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-DEBUG] Membership level in getRotoGptSubscription: 7
[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-DEBUG] Found mapping for membership level 7: Array
(
    [membership_level_id] => 7
    [rotogpt_subscription_type] => standard_50
)

[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-DEBUG] Membership level in getRotoGptSubscription: 7
[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-DEBUG] Found mapping for membership level 7: Array
(
    [membership_level_id] => 7
    [rotogpt_subscription_type] => standard_50
)

[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-INFO] DTC Registration: Blocking proration fee for downgrade from ChatDTC (standard_50) to regular DTC membership (level 10) - fee amount: -4.99
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 533
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 537
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 541
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 565
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 499
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 533
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 537
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 541
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 565
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 533
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 537
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 541
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 565
[28-Jun-2025 07:33:31 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 499
[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-INFO] DTC Membership Status Transition: Membership #34625 transitioned from new to pending
[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-DEBUG] Membership level in getRotoGptSubscription: 10
[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-INFO] DTC RotoGPT: New membership added - membership ID: 34625
[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-INFO] DTC RotoGPT Processing: Processing membership #34625 - membership_level: 10, customer_id: 25827, membership_status: pending, is_upgrade: 1
[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-INFO] DTC RotoGPT: Membership #34625 is an upgrade/downgrade - using update endpoint
[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-DEBUG] Membership level in getRotoGptSubscription: 7
[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-DEBUG] Found mapping for membership level 7: Array
(
    [membership_level_id] => 7
    [rotogpt_subscription_type] => standard_50
)

[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-DEBUG] Membership level in getRotoGptSubscription: 10
[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-INFO] DTC Downgrade Fix: Detected downgrade - preserving current membership until expiration
[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-INFO] DTC Downgrade Fix: Preserving expiration date: July 28, 2025
[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-INFO] DTC Downgrade Fix: New membership will expire on: 2025-08-28 00:00:00
[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-INFO] DTC Downgrade Fix: Successfully scheduled downgrade for July 28, 2025
[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-INFO] DTC RotoGPT Signin: Sending request to https://api.dev.rotogpt.com/signin
[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-DEBUG] DTC RotoGPT Signin: Request body: Array
(
    [client_id] => DTC
    [client_password] => 7aMBpDiOxWKBL9H1-wF9LPSIKI_SoNOtH7vnz_naLno=
    [current_user] => Array
        (
            [user_id] => 25827
            [membership] => free
            [sign_up_date] => 28-07-2025 00:00:00
        )

)

[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-DEBUG] DTC RotoGPT Signin: Response: Array
(
    [headers] => WpOrg\Requests\Utility\CaseInsensitiveDictionary Object
        (
            [data:protected] => Array
                (
                    [date] => Sat, 28 Jun 2025 07:33:31 GMT
                    [server] => uvicorn
                    [content-length] => 713
                    [content-type] => application/json
                    [via] => 1.1 google
                    [alt-svc] => h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
                )

        )

    [body] => {"accessToken":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnQiOnsiY2xpZW50X2lkIjoiRFRDIiwiaWRfdG9fdXNlIjoiZHRjSWQiLCJjbGllbnRfbmFtZV90b191c2UiOiJEVEMifSwidXNlcl9pZCI6IjI1ODI3IiwibWVtYmVyc2hpcCI6ImZyZWUiLCJzaWduX3VwX2RhdGUiOiIyOC0wNy0yMDI1IDAwOjAwOjAwIiwidXNlckRhdGEiOnsiY3JlZGl0cyI6NTAsImFsbG93ZWRfbW9kZWxzIjpbIlJvdG9HUFQgNC4wIiwiUm90b0dQVCAzLjUiLCJSb3RvR1BUIDMuNS4xIiwiUm90b0dQVCA0LjUiLCJSb3RvR1BUIDUuMCJdLCJ0ZXJtc0FuZENvbmRpdGlvbnMiOnsidGVybXNBbmRDb25kaXRpb25JZCI6ImM4ODNkNzRmLTc1ZTUtNGE0OS1hNDhmLTlkNTc3MWE1MDM1MSIsInRlcm1zQW5kQ29uZGl0aW9uIjoiVGhpcyBpcyBzb21lIHRlcm1zIHlvISBZb3UgYmV0dGVyIGFjY2VwdCEifX0sInVzZXJTdGF0dXMiOiJub3Rfc3VydmV5IiwiZXhwIjoxNzUyMzA1NjExfQ.-R8b0gSng7BsipfxrCZp5ROnizyXS9YY_4oZCNeX3pE"}
    [response] => Array
        (
            [code] => 200
            [message] => OK
        )

    [cookies] => Array
        (
        )

    [filename] => 
    [http_response] => WP_HTTP_Requests_Response Object
        (
            [response:protected] => WpOrg\Requests\Response Object
                (
                    [body] => {"accessToken":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnQiOnsiY2xpZW50X2lkIjoiRFRDIiwiaWRfdG9fdXNlIjoiZHRjSWQiLCJjbGllbnRfbmFtZV90b191c2UiOiJEVEMifSwidXNlcl9pZCI6IjI1ODI3IiwibWVtYmVyc2hpcCI6ImZyZWUiLCJzaWduX3VwX2RhdGUiOiIyOC0wNy0yMDI1IDAwOjAwOjAwIiwidXNlckRhdGEiOnsiY3JlZGl0cyI6NTAsImFsbG93ZWRfbW9kZWxzIjpbIlJvdG9HUFQgNC4wIiwiUm90b0dQVCAzLjUiLCJSb3RvR1BUIDMuNS4xIiwiUm90b0dQVCA0LjUiLCJSb3RvR1BUIDUuMCJdLCJ0ZXJtc0FuZENvbmRpdGlvbnMiOnsidGVybXNBbmRDb25kaXRpb25JZCI6ImM4ODNkNzRmLTc1ZTUtNGE0OS1hNDhmLTlkNTc3MWE1MDM1MSIsInRlcm1zQW5kQ29uZGl0aW9uIjoiVGhpcyBpcyBzb21lIHRlcm1zIHlvISBZb3UgYmV0dGVyIGFjY2VwdCEifX0sInVzZXJTdGF0dXMiOiJub3Rfc3VydmV5IiwiZXhwIjoxNzUyMzA1NjExfQ.-R8b0gSng7BsipfxrCZp5ROnizyXS9YY_4oZCNeX3pE"}
                    [raw] => HTTP/1.1 200 OK
date: Sat, 28 Jun 2025 07:33:31 GMT
server: uvicorn
Content-Length: 713
content-type: application/json
Via: 1.1 google
Alt-Svc: h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
Connection: close

{"accessToken":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnQiOnsiY2xpZW50X2lkIjoiRFRDIiwiaWRfdG9fdXNlIjoiZHRjSWQiLCJjbGllbnRfbmFtZV90b191c2UiOiJEVEMifSwidXNlcl9pZCI6IjI1ODI3IiwibWVtYmVyc2hpcCI6ImZyZWUiLCJzaWduX3VwX2RhdGUiOiIyOC0wNy0yMDI1IDAwOjAwOjAwIiwidXNlckRhdGEiOnsiY3JlZGl0cyI6NTAsImFsbG93ZWRfbW9kZWxzIjpbIlJvdG9HUFQgNC4wIiwiUm90b0dQVCAzLjUiLCJSb3RvR1BUIDMuNS4xIiwiUm90b0dQVCA0LjUiLCJSb3RvR1BUIDUuMCJdLCJ0ZXJtc0FuZENvbmRpdGlvbnMiOnsidGVybXNBbmRDb25kaXRpb25JZCI6ImM4ODNkNzRmLTc1ZTUtNGE0OS1hNDhmLTlkNTc3MWE1MDM1MSIsInRlcm1zQW5kQ29uZGl0aW9uIjoiVGhpcyBpcyBzb21lIHRlcm1zIHlvISBZb3UgYmV0dGVyIGFjY2VwdCEifX0sInVzZXJTdGF0dXMiOiJub3Rfc3VydmV5IiwiZXhwIjoxNzUyMzA1NjExfQ.-R8b0gSng7BsipfxrCZp5ROnizyXS9YY_4oZCNeX3pE"}
                    [headers] => WpOrg\Requests\Response\Headers Object
                        (
                            [data:protected] => Array
                                (
                                    [date] => Array
                                        (
                                            [0] => Sat, 28 Jun 2025 07:33:31 GMT
                                        )

                                    [server] => Array
                                        (
                                            [0] => uvicorn
                                        )

                                    [content-length] => Array
                                        (
                                            [0] => 713
                                        )

                                    [content-type] => Array
                                        (
                                            [0] => application/json
                                        )

                                    [via] => Array
                                        (
                                            [0] => 1.1 google
                                        )

                                    [alt-svc] => Array
                                        (
                                            [0] => h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
                                        )

                                )

                        )

                    [status_code] => 200
                    [protocol_version] => 1.1
                    [success] => 1
                    [redirects] => 0
                    [url] => https://api.dev.rotogpt.com/signin
                    [history] => Array
                        (
                        )

                    [cookies] => WpOrg\Requests\Cookie\Jar Object
                        (
                            [cookies:protected] => Array
                                (
                                )

                        )

                )

            [filename:protected] => 
            [data] => 
            [headers] => 
            [status] => 
        )

)

[28-Jun-2025 07:33:31 UTC] [2025-06-28 07:33:31] [DTC-DEBUG] DTC RotoGPT Signin: Response body: Array
(
    [accessToken] => eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnQiOnsiY2xpZW50X2lkIjoiRFRDIiwiaWRfdG9fdXNlIjoiZHRjSWQiLCJjbGllbnRfbmFtZV90b191c2UiOiJEVEMifSwidXNlcl9pZCI6IjI1ODI3IiwibWVtYmVyc2hpcCI6ImZyZWUiLCJzaWduX3VwX2RhdGUiOiIyOC0wNy0yMDI1IDAwOjAwOjAwIiwidXNlckRhdGEiOnsiY3JlZGl0cyI6NTAsImFsbG93ZWRfbW9kZWxzIjpbIlJvdG9HUFQgNC4wIiwiUm90b0dQVCAzLjUiLCJSb3RvR1BUIDMuNS4xIiwiUm90b0dQVCA0LjUiLCJSb3RvR1BUIDUuMCJdLCJ0ZXJtc0FuZENvbmRpdGlvbnMiOnsidGVybXNBbmRDb25kaXRpb25JZCI6ImM4ODNkNzRmLTc1ZTUtNGE0OS1hNDhmLTlkNTc3MWE1MDM1MSIsInRlcm1zQW5kQ29uZGl0aW9uIjoiVGhpcyBpcyBzb21lIHRlcm1zIHlvISBZb3UgYmV0dGVyIGFjY2VwdCEifX0sInVzZXJTdGF0dXMiOiJub3Rfc3VydmV5IiwiZXhwIjoxNzUyMzA1NjExfQ.-R8b0gSng7BsipfxrCZp5ROnizyXS9YY_4oZCNeX3pE
)

[28-Jun-2025 07:33:32 UTC] [2025-06-28 07:33:32] [DTC-INFO] DTC RotoGPT Update: Response code: 200
[28-Jun-2025 07:33:32 UTC] [2025-06-28 07:33:32] [DTC-DEBUG] DTC RotoGPT Update: Response body: {"status":"success","message":"Subscription change for user 25827 to free has been scheduled for 2025-07-28.","user_id":"25827"}
[28-Jun-2025 07:33:32 UTC] [2025-06-28 07:33:32] [DTC-INFO] DTC RotoGPT Downgrade Success: Scheduled user #25827 from standard_50 to free starting 2025-07-28T00:00:00Z
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 533
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 537
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 541
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 565
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 503
[28-Jun-2025 07:33:32 UTC] PHP Deprecated:  Function RCP_Registration::get_subscription is <strong>deprecated</strong> since version 3.0! Use RCP_Registration:get_membership_level_id instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 533
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 537
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 541
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 565
[28-Jun-2025 07:33:32 UTC] PHP Warning:  file_get_contents(): SSL operation failed with code 1. OpenSSL Error messages:
error:0A000126:SSL routines::unexpected eof while reading in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/dynasty-trade-calculator/user/rcp.php on line 58
[28-Jun-2025 07:33:32 UTC] PHP Warning:  file_get_contents(): SSL: Undefined error: 0 in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/dynasty-trade-calculator/user/rcp.php on line 58
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 533
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 537
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 541
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 565
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 499
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 533
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 537
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 541
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 565
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 499
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 503
[28-Jun-2025 07:33:32 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 499
[28-Jun-2025 07:33:33 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:33 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:33 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:33 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:33 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:33 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affwp-paypal-payouts</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:33 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:33 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:33 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:34 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:35 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:35 UTC] [2025-06-28 07:33:35] [DTC-DEBUG] Membership level in getRotoGptSubscription: 7
[28-Jun-2025 07:33:35 UTC] [2025-06-28 07:33:35] [DTC-DEBUG] Found mapping for membership level 7: Array
(
    [membership_level_id] => 7
    [rotogpt_subscription_type] => standard_50
)

[28-Jun-2025 07:33:35 UTC] [2025-06-28 07:33:35] [DTC-DEBUG] Membership level in getRotoGptSubscription: 7
[28-Jun-2025 07:33:35 UTC] [2025-06-28 07:33:35] [DTC-DEBUG] Found mapping for membership level 7: Array
(
    [membership_level_id] => 7
    [rotogpt_subscription_type] => standard_50
)

[28-Jun-2025 07:33:35 UTC] [2025-06-28 07:33:35] [DTC-INFO] DTC Registration: Blocking proration fee for downgrade from ChatDTC (standard_50) to regular DTC membership (level 10) - fee amount: -4.99
[28-Jun-2025 07:33:35 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:35 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 533
[28-Jun-2025 07:33:35 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 537
[28-Jun-2025 07:33:35 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 541
[28-Jun-2025 07:33:35 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 565
[28-Jun-2025 07:33:35 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 499
[28-Jun-2025 07:33:35 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 503
[28-Jun-2025 07:33:35 UTC] PHP Warning:  Trying to access array offset on value of type bool in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/restrict-content-pro/core/includes/class-rcp-registration.php on line 499
[28-Jun-2025 07:33:36 UTC] [2025-06-28 07:33:36] [DTC-DEBUG] Membership level in getRotoGptSubscription: 7
[28-Jun-2025 07:33:36 UTC] [2025-06-28 07:33:36] [DTC-DEBUG] Found mapping for membership level 7: Array
(
    [membership_level_id] => 7
    [rotogpt_subscription_type] => standard_50
)

[28-Jun-2025 07:33:36 UTC] [2025-06-28 07:33:36] [DTC-DEBUG] Membership level in getRotoGptSubscription: 10
[28-Jun-2025 07:33:36 UTC] [2025-06-28 07:33:36] [DTC-INFO] DTC Downgrade Fix: Preventing automatic activation of scheduled downgrade membership #34625
[28-Jun-2025 07:33:36 UTC] [2025-06-28 07:33:36] [DTC-INFO] DTC Downgrade Fix: Completed downgrade registration without activation for payment #134209
[28-Jun-2025 07:33:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:40 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:40 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:40 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/calculator
[28-Jun-2025 07:33:40 UTC] DTC DEBUG: Current page - ID: 11916, URL: /calculator, RCP subscription page ID: 0
[28-Jun-2025 07:33:40 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:40 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:40 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:41 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:41 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/calculator
[28-Jun-2025 07:33:41 UTC] DTC DEBUG: Current page - ID: 11916, URL: /calculator/, RCP subscription page ID: 0
[28-Jun-2025 07:33:41 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:41 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:41 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:41 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:41 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:42 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:42 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:42 UTC] [2025-06-28 07:33:42] [DTC-DEBUG] Membership level in getRotoGptSubscription: 7
[28-Jun-2025 07:33:42 UTC] [2025-06-28 07:33:42] [DTC-DEBUG] Found mapping for membership level 7: Array
(
    [membership_level_id] => 7
    [rotogpt_subscription_type] => standard_50
)

[28-Jun-2025 07:33:42 UTC] [2025-06-28 07:33:42] [DTC-INFO] DTC RotoGPT Signin: Sending request to https://api.dev.rotogpt.com/signin
[28-Jun-2025 07:33:42 UTC] [2025-06-28 07:33:42] [DTC-DEBUG] DTC RotoGPT Signin: Request body: Array
(
    [client_id] => DTC
    [client_password] => 7aMBpDiOxWKBL9H1-wF9LPSIKI_SoNOtH7vnz_naLno=
    [current_user] => Array
        (
            [user_id] => 25827
            [membership] => standard_50
            [sign_up_date] => 28-06-2025 01:38:55
        )

)

[28-Jun-2025 07:33:42 UTC] [2025-06-28 07:33:42] [DTC-DEBUG] DTC RotoGPT Signin: Response: Array
(
    [headers] => WpOrg\Requests\Utility\CaseInsensitiveDictionary Object
        (
            [data:protected] => Array
                (
                    [date] => Sat, 28 Jun 2025 07:33:41 GMT
                    [server] => uvicorn
                    [content-length] => 722
                    [content-type] => application/json
                    [via] => 1.1 google
                    [alt-svc] => h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
                )

        )

    [body] => {"accessToken":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnQiOnsiY2xpZW50X2lkIjoiRFRDIiwiaWRfdG9fdXNlIjoiZHRjSWQiLCJjbGllbnRfbmFtZV90b191c2UiOiJEVEMifSwidXNlcl9pZCI6IjI1ODI3IiwibWVtYmVyc2hpcCI6InN0YW5kYXJkXzUwIiwic2lnbl91cF9kYXRlIjoiMjgtMDYtMjAyNSAwMTozODo1NSIsInVzZXJEYXRhIjp7ImNyZWRpdHMiOjUwLCJhbGxvd2VkX21vZGVscyI6WyJSb3RvR1BUIDQuMCIsIlJvdG9HUFQgMy41IiwiUm90b0dQVCAzLjUuMSIsIlJvdG9HUFQgNC41IiwiUm90b0dQVCA1LjAiXSwidGVybXNBbmRDb25kaXRpb25zIjp7InRlcm1zQW5kQ29uZGl0aW9uSWQiOiJjODgzZDc0Zi03NWU1LTRhNDktYTQ4Zi05ZDU3NzFhNTAzNTEiLCJ0ZXJtc0FuZENvbmRpdGlvbiI6IlRoaXMgaXMgc29tZSB0ZXJtcyB5byEgWW91IGJldHRlciBhY2NlcHQhIn19LCJ1c2VyU3RhdHVzIjoibm90X3N1cnZleSIsImV4cCI6MTc1MjMwNTYyMn0.FdPMR1-95U-gcjHkUpAY72NHjqrdOEA_QnATqJdJhE4"}
    [response] => Array
        (
            [code] => 200
            [message] => OK
        )

    [cookies] => Array
        (
        )

    [filename] => 
    [http_response] => WP_HTTP_Requests_Response Object
        (
            [response:protected] => WpOrg\Requests\Response Object
                (
                    [body] => {"accessToken":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnQiOnsiY2xpZW50X2lkIjoiRFRDIiwiaWRfdG9fdXNlIjoiZHRjSWQiLCJjbGllbnRfbmFtZV90b191c2UiOiJEVEMifSwidXNlcl9pZCI6IjI1ODI3IiwibWVtYmVyc2hpcCI6InN0YW5kYXJkXzUwIiwic2lnbl91cF9kYXRlIjoiMjgtMDYtMjAyNSAwMTozODo1NSIsInVzZXJEYXRhIjp7ImNyZWRpdHMiOjUwLCJhbGxvd2VkX21vZGVscyI6WyJSb3RvR1BUIDQuMCIsIlJvdG9HUFQgMy41IiwiUm90b0dQVCAzLjUuMSIsIlJvdG9HUFQgNC41IiwiUm90b0dQVCA1LjAiXSwidGVybXNBbmRDb25kaXRpb25zIjp7InRlcm1zQW5kQ29uZGl0aW9uSWQiOiJjODgzZDc0Zi03NWU1LTRhNDktYTQ4Zi05ZDU3NzFhNTAzNTEiLCJ0ZXJtc0FuZENvbmRpdGlvbiI6IlRoaXMgaXMgc29tZSB0ZXJtcyB5byEgWW91IGJldHRlciBhY2NlcHQhIn19LCJ1c2VyU3RhdHVzIjoibm90X3N1cnZleSIsImV4cCI6MTc1MjMwNTYyMn0.FdPMR1-95U-gcjHkUpAY72NHjqrdOEA_QnATqJdJhE4"}
                    [raw] => HTTP/1.1 200 OK
date: Sat, 28 Jun 2025 07:33:41 GMT
server: uvicorn
Content-Length: 722
content-type: application/json
Via: 1.1 google
Alt-Svc: h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
Connection: close

{"accessToken":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnQiOnsiY2xpZW50X2lkIjoiRFRDIiwiaWRfdG9fdXNlIjoiZHRjSWQiLCJjbGllbnRfbmFtZV90b191c2UiOiJEVEMifSwidXNlcl9pZCI6IjI1ODI3IiwibWVtYmVyc2hpcCI6InN0YW5kYXJkXzUwIiwic2lnbl91cF9kYXRlIjoiMjgtMDYtMjAyNSAwMTozODo1NSIsInVzZXJEYXRhIjp7ImNyZWRpdHMiOjUwLCJhbGxvd2VkX21vZGVscyI6WyJSb3RvR1BUIDQuMCIsIlJvdG9HUFQgMy41IiwiUm90b0dQVCAzLjUuMSIsIlJvdG9HUFQgNC41IiwiUm90b0dQVCA1LjAiXSwidGVybXNBbmRDb25kaXRpb25zIjp7InRlcm1zQW5kQ29uZGl0aW9uSWQiOiJjODgzZDc0Zi03NWU1LTRhNDktYTQ4Zi05ZDU3NzFhNTAzNTEiLCJ0ZXJtc0FuZENvbmRpdGlvbiI6IlRoaXMgaXMgc29tZSB0ZXJtcyB5byEgWW91IGJldHRlciBhY2NlcHQhIn19LCJ1c2VyU3RhdHVzIjoibm90X3N1cnZleSIsImV4cCI6MTc1MjMwNTYyMn0.FdPMR1-95U-gcjHkUpAY72NHjqrdOEA_QnATqJdJhE4"}
                    [headers] => WpOrg\Requests\Response\Headers Object
                        (
                            [data:protected] => Array
                                (
                                    [date] => Array
                                        (
                                            [0] => Sat, 28 Jun 2025 07:33:41 GMT
                                        )

                                    [server] => Array
                                        (
                                            [0] => uvicorn
                                        )

                                    [content-length] => Array
                                        (
                                            [0] => 722
                                        )

                                    [content-type] => Array
                                        (
                                            [0] => application/json
                                        )

                                    [via] => Array
                                        (
                                            [0] => 1.1 google
                                        )

                                    [alt-svc] => Array
                                        (
                                            [0] => h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
                                        )

                                )

                        )

                    [status_code] => 200
                    [protocol_version] => 1.1
                    [success] => 1
                    [redirects] => 0
                    [url] => https://api.dev.rotogpt.com/signin
                    [history] => Array
                        (
                        )

                    [cookies] => WpOrg\Requests\Cookie\Jar Object
                        (
                            [cookies:protected] => Array
                                (
                                )

                        )

                )

            [filename:protected] => 
            [data] => 
            [headers] => 
            [status] => 
        )

)

[28-Jun-2025 07:33:42 UTC] [2025-06-28 07:33:42] [DTC-DEBUG] DTC RotoGPT Signin: Response body: Array
(
    [accessToken] => eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnQiOnsiY2xpZW50X2lkIjoiRFRDIiwiaWRfdG9fdXNlIjoiZHRjSWQiLCJjbGllbnRfbmFtZV90b191c2UiOiJEVEMifSwidXNlcl9pZCI6IjI1ODI3IiwibWVtYmVyc2hpcCI6InN0YW5kYXJkXzUwIiwic2lnbl91cF9kYXRlIjoiMjgtMDYtMjAyNSAwMTozODo1NSIsInVzZXJEYXRhIjp7ImNyZWRpdHMiOjUwLCJhbGxvd2VkX21vZGVscyI6WyJSb3RvR1BUIDQuMCIsIlJvdG9HUFQgMy41IiwiUm90b0dQVCAzLjUuMSIsIlJvdG9HUFQgNC41IiwiUm90b0dQVCA1LjAiXSwidGVybXNBbmRDb25kaXRpb25zIjp7InRlcm1zQW5kQ29uZGl0aW9uSWQiOiJjODgzZDc0Zi03NWU1LTRhNDktYTQ4Zi05ZDU3NzFhNTAzNTEiLCJ0ZXJtc0FuZENvbmRpdGlvbiI6IlRoaXMgaXMgc29tZSB0ZXJtcyB5byEgWW91IGJldHRlciBhY2NlcHQhIn19LCJ1c2VyU3RhdHVzIjoibm90X3N1cnZleSIsImV4cCI6MTc1MjMwNTYyMn0.FdPMR1-95U-gcjHkUpAY72NHjqrdOEA_QnATqJdJhE4
)

[28-Jun-2025 07:33:42 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:42 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:43 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affwp-paypal-payouts</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:43 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:43 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:44 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:44 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:44 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/sw.js
[28-Jun-2025 07:33:44 UTC] DTC DEBUG: Current page - ID: , URL: /sw.js, RCP subscription page ID: 0
[28-Jun-2025 07:33:44 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:44 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:44 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/manifest.json
[28-Jun-2025 07:33:44 UTC] DTC DEBUG: Current page - ID: , URL: /manifest.json, RCP subscription page ID: 0
[28-Jun-2025 07:33:44 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:44 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:44 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:44 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:44 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:44 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:44 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:44 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:45 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:45 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:45 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:45 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:47 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:47 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:47 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:47 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:47 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:47 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:47 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:47 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:47 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/subscription
[28-Jun-2025 07:33:47 UTC] DTC DEBUG: Current page - ID: 31442, URL: /subscription/, RCP subscription page ID: 0
[28-Jun-2025 07:33:47 UTC] DTC DEBUG: This appears to be a subscription page based on URL
[28-Jun-2025 07:33:47 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:47 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:47 UTC] DTC DEBUG: On subscription page (ID: 31442, URL: /subscription/), enqueuing assets to hide pending memberships
[28-Jun-2025 07:33:47 UTC] DTC DEBUG: Starting auto-renewal configuration audit
[28-Jun-2025 07:33:47 UTC] DTC DEBUG: Global auto-renewal behavior: 3 (1=always, 2=never, 3=customer choice)
[28-Jun-2025 07:33:47 UTC] DTC DEBUG: Level 7 (Calculator with ChatDTC (50 Credits)) - Price: $4.99, Free: no, Lifetime: no
[28-Jun-2025 07:33:47 UTC] DTC DEBUG: Level 7 should auto-renew: yes
[28-Jun-2025 07:33:47 UTC] DTC DEBUG: Level 9 (Calculator with ChatDTC (100 Credits)) - Price: $6.99, Free: no, Lifetime: no
[28-Jun-2025 07:33:47 UTC] DTC DEBUG: Level 9 should auto-renew: yes
[28-Jun-2025 07:33:47 UTC] DTC DEBUG: Level 12 (Calculator with ChatDTC (Unlimited Credits)) - Price: $9.99, Free: no, Lifetime: no
[28-Jun-2025 07:33:47 UTC] DTC DEBUG: Level 12 should auto-renew: yes
[28-Jun-2025 07:33:47 UTC] DTC DEBUG: Membership 34625 (Level: 10, Status: pending) - Auto-renew: yes
[28-Jun-2025 07:33:47 UTC] DTC DEBUG: Membership 34623 (Level: 7, Status: active) - Auto-renew: yes
[28-Jun-2025 07:33:47 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:47 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:48 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:48 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:48 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:48 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:48 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:49 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:49 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:33:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:49 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:49 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:49 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/manifest.json
[28-Jun-2025 07:33:49 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/sw.js
[28-Jun-2025 07:33:49 UTC] DTC DEBUG: Current page - ID: , URL: /manifest.json, RCP subscription page ID: 0
[28-Jun-2025 07:33:49 UTC] DTC DEBUG: Current page - ID: , URL: /sw.js, RCP subscription page ID: 0
[28-Jun-2025 07:33:49 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:49 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:49 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:49 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:49 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:49 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:33:50 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:33:50 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
