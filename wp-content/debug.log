
[28-Jun-2025 07:15:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:24 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:15:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:25 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:25 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/subscription
[28-Jun-2025 07:15:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:25 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:25 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:25 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:15:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:25 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:25 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:26 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:15:27 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:15:28 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:28 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:28 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:28 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:15:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] DTC DEBUG: Membership init() method called
[28-Jun-2025 07:15:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/manifest.json
[28-Jun-2025 07:15:30 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] DTC DEBUG: Page load - URL: https://dynastytradecalculator.test/sw.js
[28-Jun-2025 07:15:30 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:30 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:15:31 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:15:31 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:15:31 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[28-Jun-2025 07:15:31 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:31 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:31 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[28-Jun-2025 07:15:31 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
